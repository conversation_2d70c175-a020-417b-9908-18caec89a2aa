import 'package:design_module/utilities/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:policy_details/src/core/util/extensions.dart';
import 'package:sdui/sdui.dart';
import 'package:acko_core_utilities/deeplink_handler/DeeplinkHandler.dart';


class AssetPrePolicyEditBanner extends StatelessWidget {
  const AssetPrePolicyEditBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HealthAssetDetailsCubit, HealthAssetDetailsState>(
      buildWhen: (prev, curr) =>
          curr is HealthAssetDetailsLoaded || curr is HealthAssetDetailsLoading,
      builder: (context, state) {
        if (state is HealthAssetDetailsLoaded) {
          final prePolicyEditCardList = state.prePolicyEditCardList;

          if (prePolicyEditCardList.isNullOrEmpty) {
            return const SizedBox.shrink();
          } else {
            return SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: prePolicyEditCardList!.map((card) {
                  final prePolicyEditCardData = card.data;
                  return Container(
                    width: prePolicyEditCardList.length == 1
                        ? MediaQuery.of(context).size.width - 32
                        : null,
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.fromLTRB(16, 0, 16, 24),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          const Color(0xFFFFFFFF).withOpacity(0.024),
                          const Color(0xFFFFFFFF).withOpacity(0.12),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      border: Border(
                        left: BorderSide(
                          color: const Color(0xFFFFFFFF).withOpacity(0.16),
                        ),
                        top: BorderSide(
                          color: const Color(0xFFFFFFFF).withOpacity(0.16),
                        ),
                      ),
                      borderRadius: const BorderRadius.all(Radius.circular(16)),
                    ),
                    child: GestureDetector(
                      onTap: () {
                        context
                            .read<HealthAssetDetailsCubit>()
                            .handleAssetPageTapEvent(
                              'tap_health_evaluation_asset_view_card',
                            );
                        if (prePolicyEditCardData
                                ?.redirectUrl.isNotNullOrEmpty ??
                            false) {
                          final map =
                              prePolicyEditCardData?.redirectUrl?.getRoute();
                          if (map != null) {
                            Navigator.pushNamed(
                              context,
                              map['route'] as String,
                              arguments: map,
                            );
                          }
                        }
                      },
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SDUIText(
                                value: prePolicyEditCardData?.title,
                                textColor: colorFFFFFF,
                                textStyle: 'pSmall',
                              ),
                              const SizedBox(height: 4),
                              SDUIText(
                                value: prePolicyEditCardData?.subTitle,
                                textColor: colorFFFFFF.withOpacity(0.7),
                                textStyle: 'pXSmall',
                              ),
                            ],
                          ),
                          const Icon(
                            Icons.arrow_forward_rounded,
                            color: colorFFFFFF,
                            size: 20,
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            );
          }
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }
}
