import 'dart:collection';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/core/string_extensions.dart';
import 'package:utilities/remote_config/remote_config.dart';
extension DeeplinkHandler on String {
  static const DEEP_LINK_SCHEME = "acko.com://";

  Map<String, dynamic>? getRoute({Map<String, dynamic>? arguments}) {
    Uri uri = Uri.parse(this);
    String deeplink = this;
    if (uri.scheme.isEmpty) {
      uri = Uri.parse("https://$this");
    }
    if (uri.scheme.equalsIgnoreCase("https")) {
      deeplink = _parseUrl(uri);
    } else if (uri.scheme.equalsIgnoreCase("acko.com") &&
        (uri.toString().contains(Urls.healthNavigator) ||
            uri.toString().contains(Urls.lifeNavigator) ||
            uri.toString().contains(Urls.healthLifeNavigator))) {
      String? dl = parseHealthAndLifePageLinks(uri, DEEP_LINK_SCHEME);
      if (dl != null) {
        deeplink = dl;
      }
    }

    return _parseDeeplink(deeplink, arguments);
  }

  Map<String, dynamic>? _parseDeeplink(String deepLink,
      [Map<String, dynamic>? arguments]) {
    String? type;
    Uri uri = Uri.parse(deepLink);
    if (uri.scheme.equalsIgnoreCase("acko")) {
      type = uri.pathSegments.last;
    } else if (uri.scheme.equalsIgnoreCase("acko.com")) {
      type = uri.authority;
    }
    if (type != null) {
      arguments ??= <String, dynamic>{};
      uri.queryParameters.forEach((key, value) {
        if (value.isNotEmpty) {
          if (arguments!.containsKey("url")) {
            arguments["url"] = arguments["url"] + "&$key=$value";
          } else {
            arguments[key] = value;
          }
        }
      });

      // Handling Fragments in the url -> #
      if (arguments.isNotEmpty) {
        final keyList = arguments.keys;
        final fragment = uri.fragment;
        if (keyList.isNotEmpty && fragment.isNotEmpty) {
          arguments[keyList.last] = arguments[keyList.last] + "#" + fragment;
        }
      }

      if(type == "login" && arguments.containsKey('next_url')){
        return _parseDeeplink(arguments['next_url']);
      }

      return _setParams(type, arguments);
    }
    return null;
  }

  String _parseUrl(Uri uri) {
    String deeplink;
    String path = _getFormattedPath(uri.path);

    if (path.contains('bike-journey') || path.contains('car-journey')) {
      path.replaceAll(Routes.WEB_PAGE, Routes.WEB_PAGE_V2);
    }

    if (equalsIgnoreCase(
            Constants.BASE_URL.substring(0, Constants.BASE_URL.length - 1)) ||
        Constants.BASE_URL.equalsIgnoreCase(this) ||
        Constants.BASE_URL.equalsIgnoreCase("${this}myaccount") ||
        contains(Constants.MY_ACCOUNT_URL) ||
        equalsIgnoreCase(Constants.MY_ACCOUNT_URL_SLASH) ||
        (startsWith(Constants.webHomeUtmUrl) && uri.pathSegments.isEmpty)) {
      deeplink = "$DEEP_LINK_SCHEME${Routes.APP_HOME}";
    } else if (path.equalsIgnoreCase("/central-kyc") &&
        uri.queryParameters.containsKey("request_id")) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.WEB_PAGE_V2}?url=$this&hide_app_bar=true&hide_header=false";
    } else if (path.contains('videos')) {
      String videoId = path.split('/').last;
      deeplink = "$DEEP_LINK_SCHEME${Routes.CMS_VIDEO_PAGE}?video_id=$videoId";
    } else if (path.contains('video-popup')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.CMS_VIDEO_POPUP_PAGE}?video_id=${uri.queryParameters["video_id"]}&${uri.queryParameters["orientation"]}";
    } else if ((RemoteConfigInstance.instance
                .getData(RemoteConfigKeysSet.IS_NATIVE_PAYMENT_FLOW) ??
            true) &&
        containsIgnoreCase(Constants.PAYMENT_URL) &&
        uri.queryParameters.containsKey("id")) {
      bool enableMockPayment = RemoteConfigInstance.instance
              .getData(RemoteConfigKeysSet.ENABLE_MOCK_PAYMENT) ??
          false;

      if (enableMockPayment && kDebugMode) {
        String url =
            "https://platform-simulator-frontend-uat.internal.ackodev.com/payments?id=${uri.queryParameters["id"]}";
        deeplink = "$DEEP_LINK_SCHEME${Routes.WEB_PAGE}?url=$url";
      } else {
        if (uri.queryParameters.containsKey("client")) {
          bool showWebPayment = false;
          bool showNativePayment = RemoteConfigInstance.instance
                  .getData(RemoteConfigKeysSet.SHOW_NATIVE_PAYMENT_CAR) ??
              true;
          Map<String, dynamic>? paymentClients = jsonDecode(RemoteConfigInstance
                  .instance
                  .getData(RemoteConfigKeysSet.PAYMENT_CLIENTS) ??
              "{}");

          var clientName = uri.queryParameters["client"];
          if (paymentClients != null) {
            for (var element in (paymentClients['clients'] as List)) {
              if (element == clientName) {
                showWebPayment = true;
              }
            }
          }
          if (showWebPayment) {
            deeplink = "$DEEP_LINK_SCHEME${Routes.WEB_PAGE}?url=$this";
          } else {
            deeplink =
                "$DEEP_LINK_SCHEME${Routes.PAYMENT}?ekey=${uri.queryParameters["id"]}";
          }
        } else {
          deeplink =
              "$DEEP_LINK_SCHEME${Routes.PAYMENT}?ekey=${uri.queryParameters["id"]}";
        }
      }
    } else if (path.equalsIgnoreCase(Urls.partnerUrl!) &&
        uri.queryParameters.containsKey("group_id")) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.INTERNET_POLICY}?policy=${uri.queryParameters["group_id"]}";
    } else if (path.equalsIgnoreCase(Urls.policyDetailsUrl!)) {
      //checking multi claims url
      if (uri.queryParameters.containsKey("policy_number")) {
        deeplink =
            "$DEEP_LINK_SCHEME${Routes.CLAIMS}?policyNumber=${uri.queryParameters["policy_number"]}";
      } else {
        if (uri.queryParameters.containsKey("trackClaim")) {
          deeplink =
              "$DEEP_LINK_SCHEME${Routes.CLAIMS}?policyNumber=${uri.queryParameters["pid"]}";
        } else {
          deeplink =
              "$DEEP_LINK_SCHEME${Routes.POLICY_DETAILS_PAGE}?policyId=${uri.queryParameters["pid"]}&isFromAsset=${uri.queryParameters["isFromAsset"]}&isShowDownload=${uri.queryParameters["isShowDownload"]}";
        }
      }
    } else if (path.equalsIgnoreCase("/policyCoverage")) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.POLICY_COVERAGE_SCREEN}?regNumber=${uri.queryParameters["reg_no"]}";
    } else if (path.equalsIgnoreCase("/challan_payment_success") &&
        uri.queryParameters.containsKey("challan_id")) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.CHALLAN_PAYMENT_COMPLETION_SCREEN}?challan_id=${uri.queryParameters["challan_id"]}${uri.queryParameters["previous_screen_name"].isNotNullOrEmpty ? '&previous_screen_name=${uri.queryParameters["previous_screen_name"]}' : ''}";
    } else if (path.equalsIgnoreCase("/fastag_recharge_status_page") &&
        uri.queryParameters.containsKey("bill_recharge_payment_id")) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.FASTAG_RECHARGE_STATUS_PAGE}?bill_recharge_payment_id=${uri.queryParameters["bill_recharge_payment_id"]}&sourceScreen=${Routes.FASTAG_RECHARGE_ENTER_AMOUNT_PAGE}";
    } else if (path.equalsIgnoreCase(Urls.home!)) {
      if (uri.queryParameters.containsKey("tab")) {
        String? tab = uri.queryParameters["tab"];
        int tabIndex;
        final isV10 = _isV10();

        switch (tab) {
          case 'policies':
            tabIndex = 1;
            break;
          case 'explore':
            tabIndex = isV10 ? 0 : 2;
            break;
          case 'support':
            tabIndex = isV10 ? 2 : 3;
            break;
          default:
            tabIndex = 0;
            break;
        }
        deeplink = "$DEEP_LINK_SCHEME${Routes.APP_HOME}?tab=$tabIndex";
      } else {
        deeplink = "$DEEP_LINK_SCHEME${Routes.APP_HOME}";
      }
    } else if (path.equalsIgnoreCase(Urls.buy_card_car)) {
      deeplink = _isV10()
          ? "$DEEP_LINK_SCHEME${Routes.WEB_PAGE_V2}?url=${Constants.BUY_CARD_CAR}"
          : "$DEEP_LINK_SCHEME${Routes.APP_HOME}?home_tab=car";
    } else if (path.equalsIgnoreCase(Urls.buy_card_bike)) {
      deeplink = _isV10()
          ? "$DEEP_LINK_SCHEME${Routes.WEB_PAGE_V2}?url=${Constants.BUY_CARD_BIKE}"
          : "$DEEP_LINK_SCHEME${Routes.APP_HOME}?home_tab=bike";
    } else if (path.equalsIgnoreCase(Urls.buy_card_health)) {
      deeplink = _isV10()
          ? "$DEEP_LINK_SCHEME${Routes.WEB_PAGE_V2}?url=${Constants.BUY_CARD_HEALTH}"
          : "$DEEP_LINK_SCHEME${Routes.APP_HOME}?home_tab=health";
    } else if (path.equalsIgnoreCase(Urls.buy_card_travel)) {
      deeplink = _isV10()
          ? "$DEEP_LINK_SCHEME${Routes.WEB_PAGE_V2}?url=${Constants.BUY_CARD_TRAVEL}"
          : "$DEEP_LINK_SCHEME${Routes.APP_HOME}?home_tab=travel";
    } else if (path.equalsIgnoreCase(Urls.buy_card_life)) {
      deeplink = _isV10()
          ? "$DEEP_LINK_SCHEME${Routes.WEB_PAGE_V2}?url=${Constants.BUY_CARD_LIFE}"
          : "$DEEP_LINK_SCHEME${Routes.APP_HOME}?home_tab=life";
    } else if (path.contains('international-insurance/pdp')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.PDP_HOME}?policyId=${uri.pathSegments.last}&utmParam=${_getUtmParam(uri.queryParameters)}";
    } else if (path.contains('myaccount')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.APP_HOME}?tab=1&utmParam=${_getUtmParam(uri.queryParameters)}";
    } else if (path.contains('flight_pass') || path.contains('domestic-pass')) {
      return _handleFlightPassDeeplink(path, uri);
    } else if (path.contains('international-insurance')) {
      return _handleTravelDeeplink(path, uri);
    } else if (path.contains('p/international-travel-insurance') &&
        uri.queryParameters.containsKey('page') &&
        uri.queryParameters['page'] == 'mypolicy') {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.APP_HOME}?tab=1&utmParam=${_getUtmParam(uri.queryParameters)}";
    } else if (contains('fnol')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.WEB_PAGE_V2}?url=$this&hide_app_bar=true&hide_header=false";
    } else if (contains('/gi/auto-storefront/')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.WEB_PAGE_V2}?url=$this&hide_app_bar=true&hide_header=false";
    } else if (path.contains('/car-valuation')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.ACKO_DRIVE_VIEW}?login_use_case=${AckoDriveUtil.ackoDriveCarValuationLoginUseCase}&url=${uri.path}?${uri.query}";
    } else if (uri.toString().endsWith(".pdf")) {
      deeplink = "$DEEP_LINK_SCHEME${Routes.WEB_PAGE_V2}?url=${uri.toString()}";
    } else if (uri.host.equalsIgnoreCase(Constants.ACKO_DRIVE_HOST)) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.ACKO_DRIVE_VIEW}?login_use_case=${AckoDriveUtil.ackodriveHomePageUseCase}&url=$path?${uri.query}";
    } else if (uri.host.equalsIgnoreCase(Constants.ACKO_AWARDS_HOST) &&
        path.equalsIgnoreCase('/awards')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.ACKO_DRIVE_VIEW}?login_use_case=${AckoDriveUtil.ackoAwardsPageUseCase}&url=$path?${uri.query}";
    } else if (path.contains('rapid-response')) {
      if (path.contains('tracking') &&
          uri.queryParameters.containsKey('sosId') &&
          uri.queryParameters.containsKey('trackingStatusUseCase') &&
          uri.queryParameters.containsValue('TRACKING_PAGE')) {
        deeplink =
            "$DEEP_LINK_SCHEME${Routes.RAPID_RESPONSE_ONGOING_BOOKING_TRACKING_PAGE}?sosId=${uri.queryParameters['sosId']}&trackingStatusUseCase=${uri.queryParameters['trackingStatusUseCase']}";
      } else if (path.contains('transaction') &&
          uri.queryParameters.containsKey('sosId')) {
        deeplink =
            "$DEEP_LINK_SCHEME${Routes.RAPID_RESPONSE_TRANSACTION_COMPLETE_PAGE}?sosId=${uri.queryParameters['sosId']}&useCase=${uri.queryParameters['useCase']}";
      } else {
        deeplink = "$DEEP_LINK_SCHEME${Routes.RAPID_RESPONSE_HOME}";
      }
    } else if (path.containsIgnoreCase("/car/osr") ||
        path.containsIgnoreCase("/new-car/") ||
        path.containsIgnoreCase('car-journey') ||
        path.containsIgnoreCase('bike-journey')) {
      deeplink = "$DEEP_LINK_SCHEME${Routes.WEB_PAGE_V2}?url=$this";
    } else if (path.contains(Urls.pathPHealth) ||
        path.contains(Urls.pathHealth) ||
        path.contains('health-product')) {
      deeplink = parseHealthDeeplinks(this, uri, path, DEEP_LINK_SCHEME);
    } else if (path.contains(Urls.pathPLife) || path.contains(Urls.pathLife)) {
      deeplink = parseLifeDeeplinks(this, uri, path, DEEP_LINK_SCHEME);
    } else if (path.contains(Urls.healthNavigator) ||
        path.contains(Urls.lifeNavigator) ||
        path.contains(Urls.healthLifeNavigator)) {
      deeplink = parseHealthAndLifePageLinks(uri, DEEP_LINK_SCHEME) ??
          "$DEEP_LINK_SCHEME${Routes.APP_HOME}";
    } else if (path.contains("/abha_members")) {
      Map<String, dynamic> qParams = {};
      if (uri.queryParameters.containsKey('utm_source') &&
          uri.queryParameters['utm_source'] != null) {
        qParams['utm_source'] = uri.queryParameters['utm_source'];
      }
      if (uri.queryParameters.containsKey('utm_campaign') &&
          uri.queryParameters['utm_campaign'] != null) {
        qParams['utm_campaign'] = uri.queryParameters['utm_campaign'];
      }
      if (uri.queryParameters.containsKey('referrer') &&
          uri.queryParameters['referrer'] != null) {
        qParams['referrer'] = uri.queryParameters['referrer'];
      }
      if (uri.queryParameters.containsKey('utm_medium') &&
          uri.queryParameters['utm_medium'] != null) {
        qParams['utm_medium'] = uri.queryParameters['utm_medium'];
      }
      if (uri.queryParameters.containsKey('utm_term') &&
          uri.queryParameters['utm_term'] != null) {
        qParams['utm_term'] = uri.queryParameters['utm_term'];
      }
      qParams['path'] = path;
      deeplink = "$DEEP_LINK_SCHEME${Routes.ABHA_LANDING_PAGE}?";
      qParams.forEach((key, value) {
        deeplink += "$key=$value&";
      });
      deeplink = deeplink.substring(0, deeplink.length - 1);
    } else {
      debugPrint("AppsFlyer Deeplinkhandler $this");
      deeplink = "$DEEP_LINK_SCHEME${Routes.WEB_PAGE_V2}?url=$this";
    }
    log(deeplink);
    return deeplink;
  }

  _setDefaultUriQueryParams() {}

  Map<String, dynamic> _setParams(String type, Map<String, dynamic> params) {
    // _addProposalArgument(params);
    params["route"] = type;
    return params;
  }

  String _handleFlightPassDeeplink(String path, Uri uri) {
    String deeplink = '';
    if (path.contains('pdp')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.FLIGHT_PASS_PDP}?policyId=${uri.pathSegments.last}";
    } else if (path.contains('coverage_details')) {
      deeplink =
          '$DEEP_LINK_SCHEME${Uri(path: Routes.FLIGHT_PASS_COVERAGE_DETAIL, queryParameters: uri.queryParameters).toString()}';
    } else if (path.contains('all_claims')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.FLIGHT_PASS_CLAIMS}?policy_number=${uri.pathSegments.last}";
    } else if (path.contains('expectation')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.FLIGHT_PASS_EXPECTATION}?${uri.query}";
    } else if (path.contains('/claim')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.FLIGHT_PASS_CREATE_CLAIM}?machineId=${uri.pathSegments.last}";
    } else if (path.contains('help')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.TRAVEL_HELP}?isFlightPass=true&planType=${uri.queryParameters["planType"]}";
    } else {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.WEB_PAGE}?url=$this${uri.queryParameters.isEmpty ? '?' : '&'}hide_app_bar=true&platform=${Platform.isAndroid ? 'app_android' : 'app_ios'}";
    }
    return deeplink;
  }

  String _handleTravelDeeplink(String path, Uri uri) {
    String deeplink = '';
    if (path.contains('payment_pending')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.PAYMENT_PENDING}?proposalId=${uri.pathSegments[uri.pathSegments.length - 2]}&utmParam=${_getUtmParam(uri.queryParameters)}";
    } else if (path.contains('payment_order')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.PAYMENT_VERIFY}?orderId=${uri.pathSegments.last}";
    } else if (path.contains('claim/')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.TRAVEL_CLAIM_STATE}?machineId=${uri.pathSegments.last}";
    } else if (path.contains('claim-tracking')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.TRAVEL_CLAIM_TRACKING}?claimNumber=${uri.pathSegments.last}";
    } else if (path.contains('claim-viewall')) {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.POLICY_LIST_PAGE}?claimNumber=${uri.pathSegments.last}";
    } else if (path.contains('my_trips')) {
      deeplink = "$DEEP_LINK_SCHEME${Routes.TRAVEL_MY_TRIPS}";
    } else {
      deeplink =
          "$DEEP_LINK_SCHEME${Routes.WEB_PAGE_V2}?url=$this${uri.queryParameters.isEmpty ? '?' : '&'}hide_app_bar=true&platform=${Platform.isAndroid ? 'app_android' : 'app_ios'}";
    }
    return deeplink;
  }

  String _getUtmParam(Map<String, String> param) {
    final utmCampaign = param['utm_campaign'] ?? '';
    final utmMedium = param['utm_medium'] ?? '';
    final utmSource = param['utm_source'] ?? '';
    return '{"utm_campaign":"$utmCampaign","utm_medium":"$utmMedium","utm_source":"$utmSource"}';
  }

  String _getFormattedPath(String path) {
    if (path.endsWith("/")) {
      return path.replaceRange(path.length - 1, path.length, "");
    }
    return path;
  }

  parseHealthDeeplinks(
      String deeplinkUrl, Uri uri, String path, String deeplinkScheme) {
    debugPrint('========? parseHealthDeeplinks ${uri.toString()}');
    String deeplink;
    if (path.equalsIgnoreCase(Urls.healthClaimStatus)) {
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.IPD_TRACK_STATUS_PAGE, queryParameters: uri.queryParameters).toString()}";
    } else if (path.equalsIgnoreCase(Urls.healthHomePage)) {
      if (uri.queryParameters.containsKey('edit_policy')) {
        // Edit policy URL : https://www.acko.com/health/home?policy_id=id&edit_policy=true&product_type={enterprise/retail/aarogya_sanjeevani/aarogya_sanjeevani_v2}
        deeplink =
            "$deeplinkScheme${Uri(path: uri.queryParameters.containsKey('product_type') ? Routes.HEALTH_EDIT_ENDORSEMENT : Routes.HEALTH_HOME_PAGE, queryParameters: uri.queryParameters).toString()}";
      } else if (uri.queryParameters.containsKey('register_claim')) {
        // Register claim URL :  https://www.acko.com/health/home?register_claim=true&product_type={enterprise/retail/aarogya_sanjeevani/aarogya_sanjeevani_v2}
        String? productType = uri.queryParameters.containsKey('product_type')
            ? uri.queryParameters['product_type']
            : null;
        var jsonData = RemoteConfigInstance.instance.getData(RemoteConfigKeysSet.CLAIMS_ENTRY_CONFIG);
        final claimsEducationEnabled = jsonData['claims_education_enabled'] as bool? ?? false;
        if(claimsEducationEnabled) {
          deeplink = "$deeplinkScheme${Uri(path: Routes.HEALTH_CLAIMS_OPTION_SELECT_SCREEN, queryParameters: uri.queryParameters).toString()}";
        } else {
          deeplink = "$deeplinkScheme${Uri(path: (productType.isNotNullOrEmpty && (productType.notEqualsIgnoreCase("arogya_sanjeevni") && (productType.notEqualsIgnoreCase("arogya_sanjeevni_v2")))) ? Routes.HEALTH_CLAIMS_LIST_PAGE : Routes.ASP_CLAIMS_CONTACT_PAGE, queryParameters: uri.queryParameters).toString()}";
        }
      } else if (uri.queryParameters.containsKey('e-cards')) {
        // https://www.acko.com/health/home?policy_id=id&e-cards=true
        deeplink =
            "$deeplinkScheme${Uri(path: Routes.HEALTH_POLICY_CARDS, queryParameters: uri.queryParameters).toString()}";
      } else {
        // New home page : https://www.acko.com/health/home?policy_id=id
        // old home page : https://www.acko.com/health/home
        deeplink =
            "$deeplinkScheme${Uri(path: Routes.HEALTH_HOME_PAGE, queryParameters: <String, dynamic>{}
              ..addAll(uri.queryParameters)
              ..addAll({
                    "defaultTab": '1',
                  })).toString()}";
      }
    } else if (path.equalsIgnoreCase(Urls.acrIntro)) {
      deeplink = "$deeplinkScheme${Routes.ADVANCE_CASH_INTRO}";
    } else if (path.equalsIgnoreCase(Urls.acrLearnMore)) {
      deeplink = "$deeplinkScheme${Routes.ADVANCE_CASH_LEARN_MORE}";
    } else if (path.equalsIgnoreCase(Urls.advanceCashPayment)) {
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.ADVANCE_CASH_PAYMENT, queryParameters: uri.queryParameters).toString()}";
    } else if (path.equalsIgnoreCase(Urls.healthEcards)) {
      Map<String, dynamic> qParams = {};
      qParams['isFromCoveragesPage'] = 'true';
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.HEALTH_POLICY_CARDS, queryParameters: qParams).toString()}";
    } else if (path.equalsIgnoreCase(Urls.healthPolicyBenefits)) {
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.HEALTH_POLICY_BENEFITS, queryParameters: uri.queryParameters).toString()}";
    } else if (path.containsIgnoreCase('ahc_landing_page')) {
      Map<String, dynamic> qParams = {};
      qParams['policy_number'] = uri.queryParameters['policy_number'];
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.AHC_LANDING, queryParameters: qParams).toString()}";
    } else if (path.contains("/ppmc_booking_screen")) {
      deeplink =
      "$deeplinkScheme${Uri(path: Routes.PPMC_BOOKING_SCREEN, queryParameters: uri.queryParameters).toString()}";
    } else if (path.contains("/ppmc_booking_details_screen") ||
        path.contains("/ppmc_booking_details")) {
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.PPMC_BOOKING_DETAILS_SCREEN, queryParameters: uri.queryParameters).toString()}";
    } else if (path.contains("/ppmc_tests_landing_screen") ||
        path.contains("/ppmc_tests_scheduling")) {
      Map<String, dynamic> qParams = {};
      if (uri.queryParameters.containsKey('referenceId')) {
        qParams['referenceId'] = uri.queryParameters['referenceId'];
      }
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.PPMC_TESTS_LANDING_SCREEN, queryParameters: qParams).toString()}";
    } else if (path.contains("/status/track")) {
      Map<String, dynamic> qParams = {};
      if (uri.queryParameters.containsKey('proposal_id')) {
        qParams['referenceId'] = uri.queryParameters['proposal_id'];
      }
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.P2I_STATUS_TRACKING_SCREEN, queryParameters: qParams).toString()}";
    } else if (path.contains("/telemer_details")) {
      Map<String, dynamic> qParams = {};
      if (uri.queryParameters.containsKey('proposalId')) {
        qParams['referenceId'] = uri.queryParameters['proposalId'];
      }
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.P2I_TELEMER_SCREEN, queryParameters: qParams).toString()}";
    } else if (path.contains("/add_info_details")) {
      Map<String, dynamic> qParams = {};
      if (uri.queryParameters.containsKey('proposalId')) {
        qParams['referenceId'] = uri.queryParameters['proposalId'];
      }
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.P2I_ADD_INFO_SCREEN, queryParameters: qParams).toString()}";
    } else if (path.contains("/exclude_member")) {
      Map<String, dynamic> qParams = {};
      if (uri.queryParameters.containsKey('proposalId')) {
        qParams['referenceId'] = uri.queryParameters['proposalId'];
      }
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.P2I_MEMBER_EXCLUSION_SCREEN, queryParameters: qParams).toString()}";
    } else if (path.containsIgnoreCase('/pre_policy_edit/payment_successful')) {
      Map<String, dynamic> qParams = {};
      if (uri.queryParameters.containsKey('proposal_id')) {
        qParams['proposal_id'] = uri.queryParameters['proposal_id'];
      }
        if (uri.queryParameters.containsKey('basba_proposal')) {
        qParams['basba_proposal'] =
            uri.queryParameters['basba_proposal'];
      }
      if (uri.queryParameters.containsKey('is_mandate_enabled')) {
        qParams['is_mandate_enabled'] = uri.queryParameters['is_mandate_enabled'];
      }
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.PPE_PAYMENT_SUCCESS_SCREEN, queryParameters: qParams).toString()}";
    } else if (path.containsIgnoreCase('/pre_policy_edit') &&
        path.containsIgnoreCase('overview')) {
      Map<String, dynamic> qParams = {};
      if (uri.queryParameters.containsKey('proposal_id')) {
        qParams['proposal_id'] = uri.queryParameters['proposal_id'];
      }
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.PPE_EDIT_OVERVIEW_SCREEN, queryParameters: qParams).toString()}";
    } else if (path.containsIgnoreCase('pre_policy_edit') &&
        path.containsIgnoreCase('tracking')) {
      Map<String, dynamic> qParams = {};
      if (uri.queryParameters.containsKey('proposal_id')) {
        qParams['proposal_id'] = uri.queryParameters['proposal_id'];
      }
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.PPE_EDIT_TRACKING_SCREEN, queryParameters: qParams).toString()}";
    } else if (path.containsIgnoreCase('/gmc/handover')) {
      deeplink = "$deeplinkScheme${Routes.MERGE_ACCOUNTS_LOADER_PAGE}";
    } else if (path.contains("health/renewalV2/overview")) {
      Map<String, String> queryParameters = uri.queryParameters;
      if (queryParameters['policy_id'] != null) {
        deeplink =
            "$deeplinkScheme${Routes.WEB_PAGE}?url=${_handleDefaultWebLinks(deeplinkUrl, "false", "true")}";
      } else {
        deeplink =
            "$deeplinkScheme${Uri(path: Routes.HEALTH_RENEWAL_HOME_PAGE, queryParameters: uri.queryParameters).toString()}";
      }
    } else if (path.containsIgnoreCase('/claims/option_select_screen')) {
      deeplink =
      "$deeplinkScheme${Uri(path: Routes.HEALTH_CLAIMS_OPTION_SELECT_SCREEN, queryParameters: uri.queryParameters).toString()}";

    } else if (path.containsIgnoreCase('/claims/file_claim')) {
      Map<String, dynamic> qParams = {};
      if (uri.queryParameters.containsKey('acr_id')) {
        qParams['acr_id'] = uri.queryParameters['acr_id'];
      }
      if (uri.queryParameters.containsKey('load_from_draft')) {
        qParams['load_from_draft'] =
            uri.queryParameters['load_from_draft']?.equalsIgnoreCase('true');
      }
      if (uri.queryParameters.containsKey('incident_reason')) {
        qParams['incident_reason'] = uri.queryParameters['incident_reason'];
      }
      if (uri.queryParameters.containsKey('has_pending_payment')) {
        qParams['has_pending_payment'] =
            uri.queryParameters['has_pending_payment']?.equalsIgnoreCase('true');
      }
      if (uri.queryParameters.containsKey('is_advance_cash_flow')) {
        qParams['is_advance_cash_flow'] =
            uri.queryParameters['is_advance_cash_flow']?.equalsIgnoreCase('true');
      }
      if (uri.queryParameters.containsKey('is_claim_for_advance_cash')) {
        qParams['is_claim_for_advance_cash'] =
            uri.queryParameters['is_claim_for_advance_cash']?.equalsIgnoreCase('true');
      }
      deeplink = "$DEEP_LINK_SCHEME${Uri(path: Routes.FILE_HEALTH_FNOL, queryParameters: qParams).toString()}";
    } else if (path.containsIgnoreCase('networkhospitals')) {
      deeplink = "$deeplinkScheme${Routes.NETWORK_HOSPITALS_LIST}";
    } else if (path.contains("/policy/evaluationSteps") ||
        path.contains("/health/policy/tracker")) {
      deeplink =
          "$deeplinkScheme${Routes.WEB_PAGE_V2}?url=${_handleDefaultWebLinks(deeplinkUrl, "false", "true")}";
    } else if (path.contains('endorsementV2')) {
      deeplink =
          "$deeplinkScheme${Routes.WEB_PAGE_V2}?url=$deeplinkUrl&hide_app_bar=false&hide_header=true";
    } else if (path.contains('asset_policy_health')) {
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.ASSET_POLICY_HEALTH, queryParameters: uri.queryParameters).toString()}";
    } else if (path.containsIgnoreCase('enrollment')) {
      deeplink =
          "$deeplinkScheme${Routes.WEB_PAGE_V2}?url=${_handleDefaultWebLinks(deeplinkUrl, "true", "false")}";
      "$deeplinkScheme${Uri(path: Routes.ASSET_POLICY_HEALTH, queryParameters: uri.queryParameters).toString()}";
    } else if (path.containsIgnoreCase('policyDocuments')) {
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.POLICY_DOCUMENTS_SCREEN, queryParameters: uri.queryParameters).toString()}";
    } else {
      deeplink =
          "$deeplinkScheme${Routes.WEB_PAGE_V2}?url=${_handleDefaultWebLinks(deeplinkUrl, "false", "true")}";
    }
    debugPrint('========? parseHealthDeeplinks $deeplink');
    return deeplink;
  }

  parseLifeDeeplinks(
      String deeplinkUrl, Uri uri, String path, String deeplinkScheme) {
    debugPrint('========? parseLifeDeeplinks ${uri.toString()}');
    String deeplink;
    if (path.equalsIgnoreCase('/life/home')) {
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.LIFE_HOME_PAGE, queryParameters: uri.queryParameters).toString()}";
    } else if (path.equalsIgnoreCase('/life/medical_evaluation_status')) {
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.MEDICAL_EVALUATION_DETAILS, queryParameters: uri.queryParameters).toString()}";
    } else if (path.containsIgnoreCase('/life/purchasedPlan')) {
      deeplink =
          "$deeplinkScheme${Uri(path: Routes.LIFE_BENEFITS_PAGE, queryParameters: uri.queryParameters).toString()}";
    } else if (path.containsIgnoreCase('/p/renewal')) {
      String? journeyId;
      if (uri.queryParameters.containsKey('card_status')) {
        journeyId = uri.queryParameters['card_status'];
      } else if (uri.queryParameters.containsKey('status')) {
        journeyId = uri.queryParameters['status'];
      }
      final route = journeyId.equalsIgnoreCase('life_reinstate')
          ? Routes.LIFE_REACTIVATE_PAGE
          : Routes.LIFE_RENEWAL_PAGE;

      deeplink =
          "$deeplinkScheme${Uri(path: route, queryParameters: uri.queryParameters).toString()}";
    } else {
      deeplink =
          "$deeplinkScheme${Routes.WEB_PAGE_V2}?url=${_handleDefaultWebLinks(deeplinkUrl, "true", "false")}";
    }
    debugPrint('========? parseLifeDeeplinks $deeplink');
    return deeplink;
  }

  String _handleDefaultWebLinks(
      String deeplinkUrl, String hideAppBar, String hideHeader) {
    Uri deepLinkUri = Uri.parse(deeplinkUrl);
    Map<String, dynamic> queryParameters = HashMap();
    if (!deepLinkUri.queryParameters.containsKey("hide_app_bar")) {
      queryParameters["hide_app_bar"] = hideAppBar;
    }
    if (!deepLinkUri.queryParameters.containsKey("hide_header")) {
      queryParameters["hide_header"] = hideHeader;
      queryParameters["hide_back_arrow"] = hideHeader;
    }
    String lifeDefaultUrl = Uri(
            scheme: deepLinkUri.scheme,
            path: deepLinkUri.path,
            host: deepLinkUri.host,
            queryParameters: <String, dynamic>{}
              ..addAll(deepLinkUri.queryParameters)
              ..addAll(queryParameters))
        .toString();
    return lifeDefaultUrl;
  }

  String? parseHealthAndLifePageLinks(Uri uri, String deeplinkScheme) {
    String? deeplink;
    debugPrint('========? parseHealthAndLifePageLinks ${uri.toString()}');
    deeplink =
        "$deeplinkScheme${Uri(path: uri.toString().contains('/life-page') ? Routes.LIFE_NAVIGATOR : Routes.HEALTH_NAVIGATOR, queryParameters: <String, dynamic>{}
          ..addAll(uri.queryParameters)
          ..addAll({
                "path": uri.pathSegments.last,
                "from_page": "new_deeplink",
                //if deeplink contains doctor-consultation, labs, pharmacy
                //add query parameter 'route_animation_type = transparent
                if (uri.pathSegments.last.contains('doctor-consultation') ||
                    uri.pathSegments.last.contains('labs') ||
                    uri.pathSegments.last.contains('pharmacy'))
                  "route_animation_type": "transparent",
              })).toString()}";

    debugPrint('========? parseHealthAndLifePageLinks $deeplink');
    return deeplink;
  }

  bool _isV10() => RemoteConfigInstance.instance.getData(RemoteConfigKeysSet.APP_IA_VERSION).toString().equalsIgnoreCase("app_v10");
}
