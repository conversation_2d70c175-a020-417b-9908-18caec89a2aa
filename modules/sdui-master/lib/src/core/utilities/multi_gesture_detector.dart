import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';

class MultipleGestureDetector extends StatelessWidget {
  const MultipleGestureDetector(
      {super.key, required this.onTap, required this.child});
  final VoidCallback onTap;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return RawGestureDetector(
      gestures: {
        AllowMultipleGestureRecognizer: GestureRecognizerFactoryWithHandlers<
            AllowMultipleGestureRecognizer>(
          () => AllowMultipleGestureRecognizer(),
          (AllowMultipleGestureRecognizer instance) {
            instance.onTap = onTap;
          },
        )
      },
      behavior: HitTestBehavior.opaque,
      key: key,
      child: child,
    );
  }
}

// Custom Gesture Recognizer.
// rejectGesture() is overridden. When a gesture is rejected, this is the function that is called. By default, it disposes of the
// Recognizer and runs clean up. However we modified it so that instead the Recognizer is disposed of, it is actually manually added.
// The result is instead you have one Recognizer winning the Arena, you have two. It is a win-win.

class AllowMultipleGestureRecognizer extends TapGestureRecognizer {
  @override
  void rejectGesture(int pointer) {
    // Instead of always accepting, only accept if we're tracking this pointer
    if (primaryPointer != null && primaryPointer == pointer) {
      try {
        acceptGesture(pointer);
      } catch (e) {
        super.rejectGesture(pointer);
        // We cannot handle this case as _down is null inside TapGestureRecognizer
        //and current implementation is not user impacting - 17 June 2025
      }
    } else {
      super.rejectGesture(pointer);
    }
  }

  @override
  void dispose() {
    // Ensure proper cleanup
    if (primaryPointer != null) {
      stopTrackingPointer(primaryPointer!);
    }
    super.dispose();
  }
}
