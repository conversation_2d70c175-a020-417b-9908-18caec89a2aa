plugins {
    id "com.android.application"
    id "kotlin-android"
    id "kotlin-parcelize"
    id "dev.flutter.flutter-gradle-plugin"
    id 'kotlin-kapt'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    id 'com.google.firebase.firebase-perf'
    id 'hypersdk.plugin'
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

kapt {
    correctErrorTypes = true
}

android {
    compileSdkVersion 35
    ndkVersion flutter.ndkVersion
    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildFeatures {
        buildConfig = true
        viewBinding = true
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
        debug {
            storeFile file("${rootDir}/app/src/debug/acko-debug-key")
            storePassword "acko-android"
            keyAlias "acko-debug-key"
            keyPassword 'acko-android'
        }
    }

    defaultConfig {
        applicationId "com.acko.android"
        minSdkVersion 24
        targetSdkVersion 34
        versionCode 318
        versionName "12.0.3"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
        manifestPlaceholders = [GOOGLE_MAPS_API_KEY: getApiKey()]

    }

    buildTypes {
        profile {
            debuggable false
            minifyEnabled true
            shrinkResources true
            ext.alwaysUpdateBuildId = false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            resValue "string", "web_engage_license", "in~11b5642a0"

        }
        release {
            debuggable false
            minifyEnabled true
            shrinkResources true
            ext.alwaysUpdateBuildId = false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            resValue "string", "web_engage_license", "in~11b5642a0"

        }
        debug {
            signingConfig signingConfigs.debug
            debuggable true
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            resValue "string", "web_engage_license", "76ab297"
            FirebasePerformance {
                instrumentationEnabled true
            }

        }
    }
    productFlavors {
        flavorDimensions "version"
        prod {
            dimension "version"
            resValue "string", "app_name", "ACKO"
            buildConfigField "String", 'BASE_URL', "\"https://www.acko.com/\""
            buildConfigField "String", 'CENTRAL_APP_BFF_BASE_URL', "\"https://www.acko.com/\""
            buildConfigField "String", "HEALTH_BASE_URL", "\"https://health.acko.com/\""
            buildConfigField "int", "MAX_DIMENSION", "1280"
        }
        prod_flutter {
            dimension "version"
            resValue "string", "app_name", "ACKO"
            buildConfigField "String", 'BASE_URL', "\"https://www.acko.com/\""
            buildConfigField "String", 'CENTRAL_APP_BFF_BASE_URL', "\"https://www.acko.com/\""
            buildConfigField "String", "HEALTH_BASE_URL", "\"https://health.acko.com/\""
            buildConfigField "int", "MAX_DIMENSION", "1280"
            buildTypes.release.signingConfig signingConfigs.release
        }
        prod_beta {
            dimension "version"
            applicationIdSuffix ".beta"
            resValue "string", "app_name", "ACKO-BETA"
            buildConfigField "String", 'BASE_URL', "\"https://www.acko.com/\""
            buildConfigField "String", 'CENTRAL_APP_BFF_BASE_URL', "\"https://www.acko.com/beta/\""
            buildConfigField "String", "HEALTH_BASE_URL", "\"https://health.acko.com/\""
            buildConfigField "int", "MAX_DIMENSION", "1280"
            buildTypes.release.signingConfig signingConfigs.release
        }
        master {
            dimension "version"
            applicationIdSuffix ".master"
            resValue "string", "app_name", "ACKO-MASTER"
            buildConfigField "String", 'BASE_URL', "\"https://www.ackodev.com/\""
            buildConfigField "String", 'CENTRAL_APP_BFF_BASE_URL', "\"https://www.ackodev.com/\""
            buildConfigField "String", "HEALTH_BASE_URL", "\"https://health.ackodev.com/\""
            buildConfigField "int", "MAX_DIMENSION", "1280"
        }
    }
    buildToolsVersion "34.0.0"
    namespace 'com.acko.android'
}

flutter {
    source '../..'
}

static def getApiKey(){
    Properties props = new Properties()
    props.load(new FileInputStream(new File('secrets.properties')))
    return props['GOOGLE_MAPS_API_KEY']
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "androidx.appcompat:appcompat:1.6.1"
    implementation 'com.google.android.material:material:1.6.1'
    implementation 'com.android.support:multidex:1.0.3'

    implementation platform('com.google.firebase:firebase-bom:29.1.0')
    implementation 'com.google.firebase:firebase-core:21.1.1'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-config-ktx'
    implementation 'com.google.firebase:firebase-perf-ktx'
    implementation 'com.google.firebase:firebase-dynamic-links-ktx'
    implementation 'com.google.firebase:firebase-messaging-ktx'
    implementation 'com.google.firebase:firebase-appindexing:20.0.0'

    implementation "com.google.android.gms:play-services-identity:18.0.1"
    implementation "com.google.android.gms:play-services-auth:20.4.1"
    implementation "com.google.android.gms:play-services-auth-api-phone:18.0.1"

    implementation 'com.google.code.gson:gson:2.9.0'
    implementation 'com.webengage:android-sdk:4.0.1'
    implementation 'com.appsflyer:af-android-sdk:6.12.4'
    implementation 'com.facebook.android:facebook-android-sdk:8.2.0'
    implementation 'com.android.installreferrer:installreferrer:2.2'

    implementation 'com.google.android.gms:play-services-location:20.0.0'
    implementation 'com.google.android.gms:play-services-tagmanager:18.0.1'

    implementation 'com.google.android.play:review:2.0.1'
    implementation 'com.google.android.play:review-ktx:2.0.1'

    implementation 'com.google.android.play:app-update:2.1.0'
    implementation 'com.google.android.play:app-update-ktx:2.1.0'

    implementation "com.google.android.gms:play-services-base:18.1.0"
    implementation "com.google.android.gms:play-services-analytics:18.0.1"
    implementation 'androidx.core:core-splashscreen:1.0.1'

    implementation("io.insert-koin:koin-android:3.4.0")
    implementation("io.insert-koin:koin-core:3.4.0")

    implementation 'com.squareup.picasso:picasso:2.71828'

    implementation "androidx.cardview:cardview:1.0.0"

    implementation "androidx.constraintlayout:constraintlayout:2.1.4"

    implementation 'org.jetbrains:annotations:23.0.0'

    implementation 'com.scottyab:rootbeer-lib:0.1.0'

    implementation 'com.appsflyer:lvl:5.4.3'
    implementation 'com.segment.analytics.android:analytics:4.10.4'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.5'
}
hyperSdkPlugin {
    clientId = "acko"
    sdkVersion = "2.2.1"
}