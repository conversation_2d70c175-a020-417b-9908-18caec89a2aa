import 'dart:async';
import 'dart:io';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_logger/acko_logger.dart';
import 'package:acko_logger/events/error/app_exception.dart';
import 'package:acko_logger/model/error_model.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:networking_module/models/http_overrides.dart';
import 'package:path_provider/path_provider.dart';
import 'common/view/Application.dart';
// import 'package:flutter_driver/driver_extension.dart';

bool get isInDebugMode {
  bool inDebugMode = false;
  assert(inDebugMode = true);
  return inDebugMode;
}

void main() async {
  // enableFlutterDriverExtension();
  runZonedGuarded<Future<void>>(() async {
    WidgetsFlutterBinding.ensureInitialized();
    checkForEmulator();
    await Firebase.initializeApp();
    if (kReleaseMode) {
      debugPrint = (String? message, {int? wrapWidth}) {};
    }
    HttpOverrides.global = AckoHttpOverrides();
    runApp(Application());
    initConfigs();
    initInternalStorage();
    initErrorLogging();
  }, (error, stackTrace) async {
    await recordErrorWithTrace(error, stackTrace);
  });
}

checkForEmulator() async {
  if (kReleaseMode) {
    final deviceInfo = DeviceInfoPlugin();
    bool isEmulator = false;
    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      isEmulator = androidInfo.isPhysicalDevice == false ||
          androidInfo.brand.containsIgnoreCase('generic') ||
          androidInfo.model.containsIgnoreCase('Emulator') ||
          androidInfo.model.containsIgnoreCase('google_sdk') ||
          androidInfo.model.containsIgnoreCase('droid4x') ||
          androidInfo.model.containsIgnoreCase('emulator') ||
          androidInfo.hardware.containsIgnoreCase('goldfish') ||
          androidInfo.hardware.containsIgnoreCase('vbox86') ||
          androidInfo.hardware.containsIgnoreCase('nox');
    } else {
      final iosInfo = await deviceInfo.iosInfo;
      isEmulator = iosInfo.isPhysicalDevice == false;
    }

    if (isEmulator) {
      exit(0);
    }
  }
}

Future<void> initConfigs() async {
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);
  if (Platform.isAndroid && isInDebugMode) {
    await AndroidInAppWebViewController.setWebContentsDebuggingEnabled(true);
  }
}

Future<void> initInternalStorage() async {
  // Hive Initialization
  final appDocumentDirectory = await getApplicationDocumentsDirectory();
  // Hive.init(appDocumentDirectory.path);
}

Future<void> initErrorLogging() async {
  await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(
      true); // Enabling firebase crashlytics to capture the exceptions

  Function? originalOnError = FlutterError.onError;
  FlutterError.onError = (FlutterErrorDetails details) async {
    if (isInDebugMode) {
      // In development mode simply print to console.
      FlutterError.dumpErrorToConsole(details);
    }

    if (details.stack != null) {
      Zone.current.handleUncaughtError(details.exception, details.stack!);
    }

    await FirebaseCrashlytics.instance.recordFlutterError(
        details); // Firebase crashlytics to capture uncaught errors.
    // Forward to original handler.
    originalOnError!(details);
  };
}

Future<void> recordErrorWithTrace(error, stackTrace) async {
  final Map<String, dynamic> errorData = {
    "type": error.runtimeType.toString(),
    "timestamp": DateTime.now().toIso8601String(),
  };

  // Add specific error details based on type
  if (error is DioException) {
    errorData.addAll({
      "requestPath": error.requestOptions.path,
      "requestMethod": error.requestOptions.method,
      "statusCode": error.response?.statusCode,
      "responseData": error.response?.data.toString(),
    });
  } else if (error is FileSystemException) {
    errorData.addAll({
      "osError": error.osError?.toString(),
      "path": error.path,
    });
  } else if (error is FormatException) {
    errorData.addAll({
      "source": error.source,
      "offset": error.offset,
    });
  }

  String message = error.toString();
  if(message.contains('ackoassets.com') || (message.contains("Invalid statusCode: 404") && message.contains('.jpg')) || (message.containsIgnoreCase('SocketException'))){
    ///Known and handled cases -
    ///s3 images are sometimes blocked by ISP provider randomly
    ///Challan third party image urls does not always work but are handled
    /// so not logging (message.contains("Invalid statusCode: 404") && message.contains('.jpg'))
    return;
  }
  AckoLoggerManager.instance.logError(event: AppExceptionEvent(
    errorMessage: error.toString(), stackTrace: stackTrace, data: errorData, error: error
  ));
}

void logMessage(String message) {
  try {
    FirebaseCrashlytics.instance.log("Flutter $message");
  } catch (e) {}
}

logException(dynamic exception, {StackTrace? trace, dynamic reason}) async {
  FirebaseCrashlytics.instance.recordError(exception, trace, reason: reason);
}
