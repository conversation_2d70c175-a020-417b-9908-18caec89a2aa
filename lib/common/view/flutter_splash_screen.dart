// class FlutterSplashScreen extends StatefulWidget {
//   FlutterSplashScreen({Key? key}) : super(key: key);
//
//   @override
//   State<FlutterSplashScreen> createState() => _FlutterSplashScreenState();
// }
//
// class _FlutterSplashScreenState extends State<FlutterSplashScreen>
//     with TickerProviderStateMixin {
//   late final AnimationController _bgController = AnimationController(
//     duration: const Duration(milliseconds: 300),
//     vsync: this,
//   )..forward();
//
//   late final AnimationController _textController = AnimationController(
//     duration: const Duration(milliseconds: 500),
//     vsync: this,
//   )..forward();
//
//   late final Animation<double> _bgAnimation = CurvedAnimation(
//     parent: _bgController,
//     curve: Curves.easeIn,
//   );
//
//   @override
//   void dispose() {
//     _bgController.dispose();
//     _textController.dispose();
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       color: color121212,
//       child: Stack(
//         alignment: Alignment.center,
//         children: [
//           Align(
//             alignment: Alignment.center,
//             child: FadeTransition(
//               opacity: _bgAnimation,
//               child: Image.asset('assets/images/splash_bg.webp',
//                   height: getScreenHeight(),
//                   width: getScreenWidth(),
//                   fit: BoxFit.cover),
//             ),
//           ),
//           Positioned(
//             top: getScreenHeight() * 0.2,
//             width: getScreenWidth(),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 FadeTransition(
//                   opacity: _bgController,
//                   child: Column(
//                     children: [
//                       Image.asset(
//                         Util.getAssetImage(assetName: 'acko-new-logo.png'),
//                         height: 32,
//                         fit: BoxFit.cover,
//                       ),
//                       SizedBox(height: 16),
//                       TextEuclidBoldL26(
//                         splash_screen_text,
//                         textColor: colorFFFFFF,
//                         textAlign: TextAlign.center,
//                         textSize: 32.0,
//                         maxLines: 3,
//                       ),
//                     ],
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

import 'dart:async';

import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class FlutterSplashScreen extends StatefulWidget {
  final VoidCallback onActionCompleted;

  FlutterSplashScreen({required this.onActionCompleted});

  @override
  State<FlutterSplashScreen> createState() => _FlutterSplashScreenState();
}

class _FlutterSplashScreenState extends State<FlutterSplashScreen> {
  VideoPlayerController? _controller;
  Duration fallbackTimerDuration = Duration(milliseconds: 2500);
  Timer? _fallbackTimer;

  @override
  void initState() {
    initializeVideoController();
    initFallbackTimer();
    super.initState();
  }

  initializeVideoController() async {
    try {
      _controller =
          VideoPlayerController.asset('assets/video/splash_screen_video.mp4')
            ..initialize().then((_) {
              _controller?.play();
            }).catchError((e, stack) {
              onVideoInitError(e, stack);
            })
            ..addListener(_videoControllerListeners);
    } catch (e, stack) {
      onVideoInitError(e, stack);
    }
  }

  void onVideoInitError(dynamic e, StackTrace stack) {
    onActionCompletedCallback();
  }

  void initFallbackTimer() {
    _fallbackTimer = Timer.periodic(fallbackTimerDuration, (timer) {
      if (mounted) {
        onActionCompletedCallback();
      }
    });
  }

  _videoControllerListeners() {
    if (_controller?.value.duration == null ||
        (_controller?.value.duration.inSeconds ?? 0) == 0) {
      return;
    }
    if (_controller?.value.position == _controller?.value.duration) {
      onActionCompletedCallback();
    }
  }

  void onActionCompletedCallback() {
    widget.onActionCompleted();
    _fallbackTimer?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _getContent(),
    );
  }

  _getContent() {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      color: color121212,
      child: (_controller == null)
          ? SizedBox.shrink()
          : Align(
              alignment: Alignment.center,
              child: AspectRatio(
                  aspectRatio: MediaQuery.of(context).size.width /
                      MediaQuery.of(context).size.height,
                  child: VideoPlayer(_controller!)),
            ),
    );
  }

  @override
  void dispose() {
    _controller?.dispose();
    _controller = null;
    _fallbackTimer?.cancel();
    super.dispose();
  }
}
