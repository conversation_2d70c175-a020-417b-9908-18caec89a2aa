import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/health_life/hl_analytics_manager.dart'
    show HlAnalyticsManager;
import 'package:analytics/events/tap_events.dart';
import 'package:design_module/typography/styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:sdui/sdui.dart';

import '../../../common/util/color_constants.dart';
import '../../../common/view/router.dart';
import '../../../common/widgets/rounded_tabview_indicator.dart';
import '../../../framework/pdp/health/models/members_list_model.dart';
import '../../abha-journey/analytics/abha_journey_property_store.dart';
import '../../abha-journey/cubit/abha_journey_cubit.dart';
import '../../abha-journey/domain/repository/abha_journey_repository.dart';
import '../../asset_sdui/bloc/asset_sdui_bloc.dart';
import '../../asset_sdui/view/asset_sdui.dart';

class AssetsTabView extends StatefulWidget {
  final int defaultTab;
  final bool showBackButton;
  final String? assetId, registrationNumber;

  const AssetsTabView(
      {super.key, this.defaultTab = 0, this.showBackButton = false, this.assetId, this.registrationNumber});

  @override
  State<AssetsTabView> createState() => _AssetsTabViewState();
}

class _AssetsTabViewState extends State<AssetsTabView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late Widget _autoAssetView;
  late Widget _healthAssetView;

  late final HealthAssetDetailsCubit _healthCubit;
  late final FamilyMembersData familyMembersData;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this, initialIndex: widget.defaultTab);
    _initializeAssetView();
    _addTabChangeListener();
  }

  _addTabChangeListener() {
    _tabController.addListener(_onTabChanged);
  }

  void _onTabChanged() {
    if (!_tabController.indexIsChanging) return;

    final tabAnalytics = {
      0: {'journey': 'VEHICLE', 'cta_text': 'VEHICLE'},
      1: {'journey': 'FAMILY', 'cta_text': 'FAMILY'},
    };

    final selectedTab = tabAnalytics[_tabController.index];
    if (selectedTab != null) {
      AnalyticsTrackerManager.instance.sendEvent(
        event: TapConstants.TAP_BTN_INTERACTION,
        properties: {
          'from_page': 'asset',
          ...selectedTab,
        },
      );
    }
  }

  void _initializeAssetView() {
    // Initialize cubits only once
    _healthCubit = HealthAssetDetailsCubit(filter: {});

    _autoAssetView = MultiBlocProvider(
      providers: [
        BlocProvider<AssetSDUIBloc>(
          create: (context) => AssetSDUIBloc(widget.assetId, widget.registrationNumber),
        ),
        BlocProvider<CommonDataStoreBloc>.value(
          value: globalCommonDataStoreBloc!,
        )
      ],
      child: AssetSduiView(),
    );

    _healthAssetView = MultiBlocProvider(
      providers: [
        BlocProvider<HealthAssetDetailsCubit>.value(value: _healthCubit),
        BlocProvider<CommonDataStoreBloc>.value(
          value: globalCommonDataStoreBloc!,
        ),
        BlocProvider<AbhaJourneyCubit>.value(
          value: AbhaJourneyCubit(
            null,
            AbhaJourneyRepository(),
            HlAnalyticsManager(
              AbhaJourneyPropertyStore(),
            ),
          ),
        ),
      ],
      child: HealthAssetDetailsView(),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: color060606,
      body: Stack(
        children: [
          Column(
            children: [
              SizedBox(height: widget.showBackButton ? 64: 48),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: TabBar(
                  controller: _tabController,
                  isScrollable: false,
                  dividerColor: Colors.transparent,
                  indicator: RoundedTabIndicator(color: colorB191ED),
                  indicatorSize: TabBarIndicatorSize.tab,
                  labelColor: colorB191ED,
                  indicatorPadding: EdgeInsets.zero,
                  unselectedLabelColor: colorFFFFFF,
                  labelStyle: lMediumText.textStyle,
                  tabs: const [
                    Tab(text: 'My vehicles'),
                    Tab(text: 'My family'),
                  ],
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                ),
              ),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    _autoAssetView,
                    _healthAssetView,
                  ],
                ),
              ),
            ],
          ),
          if (widget.showBackButton)
            Positioned(
              top: 36,
              left: 0,
              child: IconButton(
                onPressed: () => Navigator.pop(context),
                icon: Icon(
                  Icons.arrow_back_ios_new_rounded,
                  color: Colors.white,
                  size: 18,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
