import 'package:acko_flutter/feature/challan_webview/utils/challan_webview_utility.dart';

import '../../challan/challan_enums.dart';
import 'challan_automation_misc_models.dart';

class PaymentStatusRequest {
  final ChallanPaymentStatus ackoPaymentStatus;
  final ChallanSearchSource source;

  final bool? shouldSendHistoryProps;
  final String? receiptUrl;
  final String? challanAmount;

  PaymentStatusRequest({
    required this.source,
    required this.ackoPaymentStatus,
    this.shouldSendHistoryProps,
    this.receiptUrl,
    this.challanAmount,
  });

  factory PaymentStatusRequest.ingestion({
    required String source,
    required ChallanPaymentStatus paymentStatus,
    String? receiptUrl,
    String? challanAmount,
  }) {
    return PaymentStatusRequest(
      source: ChallanSearchSource.forAutomationFromString(source),
      ackoPaymentStatus: paymentStatus,
      challanAmount: challanAmount,
      receiptUrl: receiptUrl,
      shouldSendHistoryProps: paymentStatus == ChallanPaymentStatus.PAYMENT_SUCCESS ||
          paymentStatus == ChallanPaymentStatus.PAYMENT_FAILED,
    );
  }

}