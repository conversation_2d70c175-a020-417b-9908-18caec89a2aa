import '../utils/challan_webview_utility.dart';
import 'challan_automation_misc_models.dart';

class KaPaymentStatusIngestionModel {
  String? success;
  String? error;

  KaPaymentStatusIngestionModel({this.success, this.error});

  KaPaymentStatusIngestionModel.error(this.error);

  KaPaymentStatusIngestionModel.success(this.success);
}

class ChallanAutomationPaymentStatus {
  ChallanPaymentStatus? paymentStatus;
  String? noticeNumber;
  String? registrationNumber;
  String? referenceNumber;
  String? error;

  ChallanAutomationPaymentStatus({this.paymentStatus, this.noticeNumber, this.referenceNumber, this.error, this.registrationNumber});

  ChallanAutomationPaymentStatus.error(this.error);

  factory ChallanAutomationPaymentStatus.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return ChallanAutomationPaymentStatus.error("API Error");
    }
    final paymentStatus = ChallanWebviewUtility.mapStringToPaymentStatus(json['payment_status']);
    return ChallanAutomationPaymentStatus(
      paymentStatus: paymentStatus,
      registrationNumber: (json['registration_number'] != null) ? json['registration_number'] : null,
      noticeNumber: (json['challan_number'] != null) ? json['challan_number'] : null,
      referenceNumber: (json['ref_number'] != null) ? json['ref_number'] : null,
    );
  }
}
