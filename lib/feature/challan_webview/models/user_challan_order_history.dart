import 'package:acko_flutter/feature/challan_sdui/model/challan_transaction_model.dart';
import 'package:acko_flutter/feature/challan_webview/utils/challan_webview_utility.dart';
import 'challan_automation_misc_models.dart';
import 'ka_challan_history_model.dart';

class UserChallanOrderHistory {
  String? noticeNumber;
  String? registrationNumber;
  String? paidBy;
  String? refNumber;
  String? challanAmount;
  String? paymentStatus;
  ChallanPaymentStatus? ackoPaymentStatus;
  List<ChallanNumberData>? challanNumbers;
  String? error;
  String? transactionDate;
  String? receiptUrl;

  UserChallanOrderHistory(
      {this.noticeNumber,
      this.registrationNumber,
      this.paidBy,
      this.refNumber,
      this.challanAmount,
      this.paymentStatus,
      this.receiptUrl,
      this.ackoPaymentStatus,
      this.transactionDate,
      this.challanNumbers});

  UserChallanOrderHistory.error(this.error);

  factory UserChallanOrderHistory.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return UserChallanOrderHistory.error("API Error");
    }

    List<ChallanNumberData>? parsedChallans;
    if (json['challan_numbers'] != null) {
      parsedChallans = (json['challan_numbers'] as List)
          .map((e) => ChallanNumberData.fromJson(e as Map<String, dynamic>))
          .toList();
    }

    return UserChallanOrderHistory(
      noticeNumber:
          (json['notice_number'] != null) ? json['notice_number'] : null,
      registrationNumber: (json['registration_number'] != null)
          ? json['registration_number']
          : null,
      paidBy: (json['paid_by'] != null) ? json['paid_by'] : null,
      refNumber: (json['ref_number'] != null) ? json['ref_number'] : null,
      challanAmount:
          (json['challan_amount'] != null) ? json['challan_amount'] : null,
      paymentStatus:
          (json['payment_status'] != null) ? json['payment_status'] : null,
      receiptUrl:
          (json['receipt_file_url'] != null) ? json['receipt_file_url'] : null,
      ackoPaymentStatus: (json['payment_status'] != null) ? ChallanWebviewUtility.mapStringToPaymentStatus(json['payment_status']) : null,
      transactionDate: (json['transaction_date'] != null) ? json['transaction_date'] : null,
        challanNumbers: parsedChallans
    );
  }

  factory UserChallanOrderHistory.fromKarnatakaChallanStatus(KaChallanHistoryModel challanStatusModel, List<ChallanNumberData>? challanNumbers) {
    return UserChallanOrderHistory(
      noticeNumber: challanStatusModel.noticeNumber,
      registrationNumber: challanStatusModel.vehicleNumber,
      refNumber: challanStatusModel.deptRefNum,
      challanAmount: challanStatusModel.challanAmount.toString(),
      ackoPaymentStatus: challanStatusModel.ackoPaymentStatus,
      transactionDate: challanStatusModel.tmcSubmissionDate,
      challanNumbers: challanNumbers,
      receiptUrl: null,
      paidBy: null,
    );
  }

  factory UserChallanOrderHistory.fromChallanHistoryArgs(
      {required List<String>? noticeNumbers,
        required String registrationNumber,
        String? challanAmount,
        required String referenceNumber,
        required ChallanPaymentStatus paymentStatus
      }) {
    return UserChallanOrderHistory(
      noticeNumber: noticeNumbers?[0],
      registrationNumber: registrationNumber,
      challanAmount: challanAmount,
      refNumber: referenceNumber,
      ackoPaymentStatus: paymentStatus,
      transactionDate: null,
      receiptUrl: null,
      paidBy: null,
    );
  }
}
