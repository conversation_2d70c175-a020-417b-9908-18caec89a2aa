import 'package:acko_flutter/feature/challan_sdui/model/challan_transaction_model.dart';
import 'package:acko_flutter/feature/challan_webview/utils/challan_webview_utility.dart';
import 'package:acko_flutter/feature/challan_webview/models/challan_keyword_model.dart';
import 'package:flutter/material.dart';

import '../../../util/Utility.dart';
import '../enums/challan_webview_enums.dart';

enum ChallanPaymentStatus {
  PAYMENT_INITIATED,
  PAYMENT_SUCCESS,
  PAYMENT_FAILED,
  PAYMENT_PENDING,
  PAYMENT_LOCKED,
  unknown
}

class ChallanAutomationCronJobModel {
  String? success;
  String? error;

  ChallanAutomationCronJobModel({this.success, this.error});

  ChallanAutomationCronJobModel.error(this.error);

  ChallanAutomationCronJobModel.success(this.success);
}

class ChallanAutomationVerifyUserUpiIds {
  String? success;
  String? error;

  ChallanAutomationVerifyUserUpiIds({this.success, this.error});

  ChallanAutomationVerifyUserUpiIds.error(this.error);

  ChallanAutomationVerifyUserUpiIds.success(this.success);
}

class ChallanAutomationDetailArgs {
  final List<String>? noticeNumbers;
  final String? registrationNumber;
  final String? violationLocation;
  final String? phoneNumber;
  final bool? shouldRunPaymentUpdateFlow;

  ChallanAutomationDetailArgs(
      {this.noticeNumbers,
      this.registrationNumber,
      this.violationLocation,
      this.phoneNumber,
      this.shouldRunPaymentUpdateFlow});

  factory ChallanAutomationDetailArgs.fromJson(Map<String, dynamic> json) {
    return ChallanAutomationDetailArgs(
      noticeNumbers: json['violation_number'] as List<String>?,
      registrationNumber: json['registration_number'] as String?,
      violationLocation: json['violation_location'] as String?,
      phoneNumber: json['phone_number'] as String?,
    );
  }
}

class ChallanHistoryArgs {
  final List<String>? noticeNumbers;
  final String? challanAmount;
  final List<ChallanNumberData>? challanNumbers;
  final String? registrationNumber;
  final String? challanSearchSource;
  final String? referenceNumber;
  ChallanPaymentStatus? paymentStatus;
  final bool? shouldRunPaymentUpdateFlow;
  final bool? shouldOpenReceipt;
  final bool? runUpdateFlow;

  ChallanHistoryArgs(
      {this.noticeNumbers,
      this.challanAmount,
      this.registrationNumber,
      this.challanNumbers,
      this.challanSearchSource,
      this.referenceNumber,
      this.paymentStatus,
      this.shouldRunPaymentUpdateFlow,
      this.shouldOpenReceipt,
      this.runUpdateFlow});

  factory ChallanHistoryArgs.fromJson(Map<String, dynamic> map) {
    return ChallanHistoryArgs(
        challanSearchSource: map['rto_source'] as String?,
        challanAmount: map['challan_amount'] as String?,
        noticeNumbers: map['notice_numbers'] as List<String>?,
        registrationNumber: map['registration_number'] as String?,
        challanNumbers: map['challan_numbers'] as List<ChallanNumberData>?,
        referenceNumber: map['ref_number'] as String?,
        paymentStatus: (map['payment_status'] != null)
            ? ChallanWebviewUtility
                .mapStringToPaymentStatus(map['payment_status'])
            : null,
        shouldRunPaymentUpdateFlow:
            map['should_run_payment_update_flow'] as bool?,
        shouldOpenReceipt: map['should_open_receipt'] as bool? ?? false,
        runUpdateFlow: map['run_update_flow'] as bool? ?? true);
  }
}

class PaymentConfirmationContent {
  final String title;
  final String subtitle;
  final String primaryButtonText;
  final String? secondaryButtonText;

  const PaymentConfirmationContent({
    required this.title,
    required this.subtitle,
    required this.primaryButtonText,
    this.secondaryButtonText,
  });
}

CaptchaProgressType captchaProgressTypeFromString(String? value) {
  switch (value) {
    case 'ready':
      return CaptchaProgressType.ready;
    case 'processing':
      return CaptchaProgressType.processing;
    default:
      return CaptchaProgressType.unknown;
  }
}

class UpiRequestResponseModel {
  String? orderId;
  String? statusCode;
  String? transactionId;
  String? merchantId;
  String? error;

  UpiRequestResponseModel({
    this.orderId,
    this.statusCode,
    this.transactionId,
    this.merchantId,
    this.error,
  });

  UpiRequestResponseModel.error(this.error);

  factory UpiRequestResponseModel.fromJson(Map<String, dynamic> json) {
    return UpiRequestResponseModel(
      orderId: json['order_id'] as String?,
      statusCode: json['status_code'] as String?,
      transactionId: json['transaction_id'] as String?,
      merchantId: json['merchant_id'] as String?,
    );
  }
}

class UpiRequestPollingResponseModel {
  String? statusCode;
  String? orderId;
  ChallanPaymentStatus? paymentStatus;
  String? error;

  UpiRequestPollingResponseModel({
    this.statusCode,
    this.orderId,
    this.paymentStatus,
    this.error,
  });

  UpiRequestPollingResponseModel.error(this.error);

  factory UpiRequestPollingResponseModel.fromJson(Map<String, dynamic> json) {
    return UpiRequestPollingResponseModel(
      statusCode: json['status_code'] as String?,
      orderId: json['order_id'] as String?,
      paymentStatus: (json['payment_status'] != null)
          ? ChallanWebviewUtility
              .mapStringToPaymentStatus(json['payment_status'])
          : null,
    );
  }
}

class UserFlowData {
  String? nextUrlOverride;
  String? userWebviewCookie;
  bool? shouldContinueDetailsFlow;
  String? paymentReferenceNumber;
  String? userUpiId;
  bool? saveUserUpiID;
  String? merchantId;
  String? bdOrderId;
  String? authToken;
  String? tsTransactionId;
  String? flowDirection;
  AmountBreakupProperties? amountBreakup;

  UserFlowData({
    this.nextUrlOverride,
    this.userWebviewCookie,
    this.shouldContinueDetailsFlow,
    this.paymentReferenceNumber,
    this.userUpiId,
    this.saveUserUpiID,
    this.amountBreakup,
    this.merchantId,
    this.bdOrderId,
    this.authToken,
    this.tsTransactionId,
    this.flowDirection,
  });
}

class DocumentServiceResponse {
  List<DocumentCategory>? categories;
  String? id;
  String? error;

  DocumentServiceResponse({this.id, this.categories});

  DocumentServiceResponse.error(this.error);

  factory DocumentServiceResponse.fromJson(Map<String, dynamic>? json) {
    final data = json?['data'];

    final categoriesJson = data?['document_category'] as List<dynamic>?;
    final id = data?['id'] as String?;

    final categories =
        categoriesJson?.map((item) => DocumentCategory.fromJson(item)).toList();

    return DocumentServiceResponse(categories: categories, id: id);
  }
}

class DocumentCategory {
  final String? documentType;
  final List<DocumentItem>? documents;

  DocumentCategory({required this.documentType, required this.documents});

  factory DocumentCategory.fromJson(Map<String, dynamic> json) {
    final docsJson = json['documents'] as List<dynamic>? ?? [];

    return DocumentCategory(
      documentType: json['document_type'] ?? '',
      documents: docsJson.map((doc) => DocumentItem.fromJson(doc)).toList(),
    );
  }
}

class DocumentItem {
  final String? id;
  final String? documentUrl;
  final bool? deleted;

  DocumentItem({
    required this.id,
    required this.documentUrl,
    required this.deleted,
  });

  factory DocumentItem.fromJson(Map<String, dynamic> json) {
    return DocumentItem(
      id: json['id'] ?? '',
      documentUrl: json['document_url'] ?? '',
      deleted: json['deleted'] ?? false,
    );
  }
}

class CaptchaCreateSduiResponse extends AckoDataDefaultResponse {
  int? taskId;
  CaptchaCreateSduiResponse({this.taskId});

  CaptchaCreateSduiResponse.fromJson(Map<String, dynamic> json) {
    taskId = json['taskId'] != null ? json['taskId'] : null;
  }
}

class CaptchaResultSduiResponse extends AckoDataDefaultResponse {
  CaptchaProgressType? captchaStatus;
  String? captchaSolution;
  CaptchaResultSduiResponse({this.captchaStatus, this.captchaSolution});

  CaptchaResultSduiResponse.fromJson(Map<String, dynamic> json) {
    captchaStatus = captchaProgressTypeFromString(json['captchaStatus']);
    captchaSolution =
        (json['captchaSolution'] != null) ? json['captchaSolution'] : null;
  }
}

class ErrorScreenConstants {
  String mainImage;
  String title;
  String subtitle;
  String buttonText;
  ErrorScreenConstants({
    required this.mainImage,
    required this.title,
    required this.subtitle,
    required this.buttonText,
  });
}

class ChallanTransactionDetailRow {
  final String label;
  final String value;
  final Color? valueColor;
  final bool isBold;
  final bool openBottomsheet;
  final List<Map<String, String>>? detailItems;

  const ChallanTransactionDetailRow({
    required this.label,
    required this.value,
    this.valueColor,
    this.isBold = false,
    this.openBottomsheet = false,
    this.detailItems,
  });
}

class DownloadReceiptAvailability {
  final bool isAvailable;
  final VoidCallback? downloadAction;

  const DownloadReceiptAvailability({
    required this.isAvailable,
    this.downloadAction,
  });
}

class TrackingScreenRowModel {
  final Color textColor;
  final String stateImage;

  TrackingScreenRowModel({required this.textColor, required this.stateImage});
}

class PaymentStatusModel {
  final bool isSuccess;
  final String text;
  final Color textColor;

  PaymentStatusModel({
    required this.isSuccess,
    required this.text,
    required this.textColor,
  });
}

class ConfirmationBottomsheetProps {
  final String title;
  final String subtitle;
  final String buttonText;

  ConfirmationBottomsheetProps({
    required this.title,
    required this.subtitle,
    required this.buttonText,
  });
}

class ChallanPaymentInProgress {
  final String title;
  final String imageUrl;

  ChallanPaymentInProgress({required this.title, required this.imageUrl});
}

class TransactionStatusModel {
  final String title;
  final String subtitle;
  final String transactionImage;

  TransactionStatusModel({
    required this.title,
    required this.subtitle,
    required this.transactionImage,
  });
}

class MaharashtraSpecificCronParams {
  final String? transactionId;
  final String? orderId;
  final String? authToken;
  final String? merchantId;
  final String? registrationNumber;

  const MaharashtraSpecificCronParams({
    this.transactionId,
    this.orderId,
    this.authToken,
    this.merchantId,
    this.registrationNumber,
  });

  Map<String, dynamic> toJson() => {
        "transaction_id": transactionId,
        "order_id": orderId,
        "auth_token": authToken,
        "merchant_id": merchantId,
        "registration_number": registrationNumber,
      };
}
