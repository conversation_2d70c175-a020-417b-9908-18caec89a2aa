import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/challan/challan_enums.dart';
import 'package:acko_flutter/feature/challan_webview/enums/challan_webview_enums.dart';
import 'package:acko_flutter/feature/challan_webview/models/user_challan_order_history.dart';
import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:acko_flutter/feature/challan_webview/widgets/challan_violations_bottomsheet.dart';
import 'package:acko_flutter/feature/challan_webview/widgets/script_error_page.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/state_provider/StateProvider.dart';

import '../../common/view/acko_text_config.dart';
import '../../util/download_util.dart';
import 'utils/challan_webview_utility.dart';
import 'challan_transaction_detail_bloc.dart';
import 'models/challan_automation_misc_models.dart';

class ChallanTransactionsDetailPage extends StatefulWidget {
  const ChallanTransactionsDetailPage({super.key});

  @override
  State<ChallanTransactionsDetailPage> createState() =>
      _ChallanTransactionsDetailPageState();
}

class _ChallanTransactionsDetailPageState
    extends State<ChallanTransactionsDetailPage> {
  ChallanTransactionDetailBloc? _bloc;
  StateProvider _stateProvider = StateProvider();
  bool _hasOpenedReceipt = false;

  @override
  void initState() {
    super.initState();
    _bloc = BlocProvider.of<ChallanTransactionDetailBloc>(context);
    _stateProvider.notify(ObserverState.CHALLAN_RELOAD,
        data: _bloc?.historyArgs.registrationNumber);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorFFFFFF,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.close,
            color: Colors.black,
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Stack(
        children: [
          Container(
            height: getScreenAwareHeight(300),
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(AssetsConstants.gradient_image),
                fit: BoxFit.cover,
              ),
            ),
          ),
          Padding(
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 8),
              child: BlocConsumer<ChallanTransactionDetailBloc,
                  ChallanTransactionDetailStates>(
                listener: (BuildContext context,
                    ChallanTransactionDetailStates state) {
                  if (state is LoadChallanWebview) {
                    final routeArguments = {
                      'challanTransactionArgs': ChallanAutomationDetailArgs(
                        noticeNumbers: _bloc?.historyArgs.noticeNumbers,
                        registrationNumber:
                            _bloc?.historyArgs.registrationNumber,
                        violationLocation:
                            _bloc!.historyArgs.challanSearchSource!,
                        phoneNumber: state.userPhoneNumber,
                      ),
                      'url': ChallanWebviewUtility.constructChallanUrl(
                          state.url, [UrlAppendAction.hideWebviewAppbar]),
                    };
                    Navigator.pushReplacementNamed(
                        context, Routes.CHALLAN_WEB_PAGE,
                        arguments: routeArguments);
                  }
                },
                builder: (prev, state) {
                  if (state is ChallanTransactionDetailLoaded) {
                    final downloadAction =
                        getDownloadAction(state.challanHistoryResponse);
                    return Column(
                      children: [
                        SizedBox(
                          height: 100,
                        ),
                        Align(
                            alignment: Alignment.centerLeft,
                            child: AckoTextConfig.i.headingMedium.text(
                                ChallanAutomationConstants.paymentDetails)),
                        SizedBox(
                          height: 20,
                        ),
                        ...state.challanDetails.asMap().entries.map(
                          (entry) {
                            final index = entry.key;
                            final detail = entry.value;
                            final showDivider =
                                index != state.challanDetails.length - 1;
                            return _buildPaymentDetailRow(
                              label: detail.label,
                              value: detail.value,
                              showDivider: showDivider,
                              isBold: detail.isBold,
                              valueColor: detail.valueColor,
                              openBottomsheet: detail.openBottomsheet,
                              detailItems: detail.detailItems,
                            );
                          },
                        ),
                        (state.isDownloadReceiptAvailable && downloadAction != null)
                            ? Padding(
                                padding:
                                    const EdgeInsets.only(bottom: 8.0, top: 24),
                                child: AckoSecondaryButtonFullWidth(
                                    icon: HybridImage(
                                      imageUrl: ChallanAutomationConstants.imageDownloadIcon,
                                      height: 24,
                                      width: 24,
                                    ),
                                    text: ChallanAutomationConstants.downloadReceipt,
                                    onTap: downloadAction),
                              )
                            : SizedBox.shrink(),
                        if ((_bloc?.historyArgs.shouldOpenReceipt ?? false) &&
                            !_hasOpenedReceipt)
                          Builder(builder: (context) {
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              if (state.isDownloadReceiptAvailable &&
                                  downloadAction != null) {
                                downloadAction();
                              }
                              _hasOpenedReceipt = true;
                            });
                            return SizedBox.shrink();
                          }),
                        _somethingNotRightWidget(),
                      ],
                    );
                  } else if (state is ChallanTransactionDetailError) {
                    return ScriptErrorPage(
                      shouldTryAgain: false,
                    );
                  } else {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Lottie.asset('assets/anim/updated_loading_anim.json',
                              height: 120, width: 120),
                          AckoTextConfig.i.headingMedium.text(
                              ChallanAutomationConstants
                                  .fetchingChallanDetails),
                        ],
                      ),
                    );
                  }
                },
              )),
        ],
      ),
    );
  }

  VoidCallback? getDownloadAction(UserChallanOrderHistory orderHistory) {
    if (orderHistory.ackoPaymentStatus == ChallanPaymentStatus.PAYMENT_FAILED) {
      return null;
    }

    if (orderHistory.receiptUrl.isNotNullOrEmpty) {
      return () async {
        String? fileExtension = ChallanWebviewUtility.getReceiptFileExtension(
            _bloc?.historyArgs.challanSearchSource!);

        AnalyticsTrackerManager.instance.sendEvent(
            event: TapConstants.TAP_BTN_CHALLANS_DOWNLOAD_RECEIPT,
            properties: {
              "status": orderHistory.paymentStatus,
            });

        await Downloader.downloadFileAndPreview(
          orderHistory.receiptUrl!,
          context,
          "challan-receipt-${orderHistory.refNumber ?? ''}${fileExtension}",
        );
      };
    }

    if (_bloc?.historyArgs.challanSearchSource == ChallanSearchSource.MH.name) {
      String? reloginUrl = _bloc?.getReloginUrl() ?? '';
      if (reloginUrl.isNullOrEmpty) {
        return null;
      }
      return () async {
        String? userPhoneNumber =
            await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);

        final updatedUrl = ChallanWebviewUtility.constructChallanUrl(
          reloginUrl,
          [
            UrlAppendAction.receiptFlow,
            UrlAppendAction.shouldOpenReceipt,
            UrlAppendAction.paymentReferenceNumber
          ],
          referenceNumber: orderHistory.refNumber,
        );
        _bloc?.loadChallanWebview(updatedUrl.toString(), userPhoneNumber!);
      };
    }

    return null;
  }

  Widget _somethingNotRightWidget() {
    return Padding(
      padding: const EdgeInsets.only(top: 24.0),
      child: Container(
        decoration: BoxDecoration(
          color: colorF5F5F5,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AckoTextConfig.i.labelSmall.text(
                  ChallanAutomationConstants.somethingNotRight,
                  textAlign: TextAlign.start),
              SizedBox(
                height: 4,
              ),
              AckoTextConfig.i.paragraphXSmall
                  .text(ChallanAutomationConstants.paymentMadeDirectlyToRto),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentDetailRow({
    required String label,
    required String value,
    bool showDivider = true,
    required bool isBold,
    required Color valueColor,
    bool openBottomsheet = false,
    List<Map<String, String>>? detailItems,
  }) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: AckoTextConfig.i.paragraphSmall.text(
                label,
                maxLines: 3,
              ),
            ),
            GestureDetector(
              onTap: openBottomsheet && detailItems != null
                  ? () => context.showAckoModalBottomSheet(
                          child: ChallanViolationsBottomsheet(
                        detailItems: detailItems,
                      ))
                  : null,
              child: Container(
                alignment: Alignment.centerRight,
                width: 150,
                child: isBold
                    ? AckoTextConfig.i.headingSmall.text(
                        value,
                        softWrap: true,
                        textAlign: TextAlign.end,
                        textColor: valueColor,
                        textDecoration: openBottomsheet
                            ? TextDecoration.underline
                            : TextDecoration.none,
                      )
                    : AckoTextConfig.i.labelSmall.text(
                        value,
                        softWrap: true,
                        textAlign: TextAlign.end,
                        textColor: valueColor,
                        textDecoration: openBottomsheet
                            ? TextDecoration.underline
                            : TextDecoration.none,
                      ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        if (showDivider) ...[
          const Divider(thickness: 1),
          const SizedBox(height: 10),
        ],
      ],
    );
  }
}
