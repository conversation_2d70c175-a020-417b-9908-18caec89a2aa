import 'dart:io';

import 'package:acko_flutter/feature/challan_webview/challan_web_view_bloc.dart';
import 'package:acko_flutter/feature/challan_webview/models/challan_automation_misc_models.dart';
import 'package:acko_flutter/feature/challan_webview/widgets/challan_webview_bottomsheet.dart';
import 'package:acko_flutter/feature/challan_webview/widgets/script_error_page.dart';
import 'package:acko_flutter/feature/challan_webview/widgets/user_input_widget.dart';
import 'package:acko_logger/acko_logger.dart';
import 'package:acko_logger/events/info/app_debug_info.dart';
import 'package:acko_web_view_module/common/custom_dartz.dart';
import 'package:acko_web_view_module/lob_contract_classes/view_contract.dart';
import 'package:acko_web_view_module/view/acko_web_view_module.dart';
import 'package:acko_web_view_module/web_view_registers.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/card_loaded_events.dart';
import 'package:design_module/design_module.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:growthbook_sdk_flutter/growthbook_sdk_flutter.dart';
import 'package:utilities/constants/constants.dart';

import '../../common/util/color_constants.dart';
import 'challan_web_logic/challan_web_view_logic.dart';
import 'models/challan_keyword_model.dart';

class ChallanWebView extends StatefulWidget {
  final String sentUrl;

  const ChallanWebView({
    Key? key,
    required this.sentUrl,
  }) : super(key: key);

  @override
  State<ChallanWebView> createState() => ChallanWebViewState();
}

class ChallanWebViewState extends State<ChallanWebView> {
  late InAppWebViewController challanWebViewController;
  String? lastLoadedUrl = "";
  ClipboardData? clipboardData;
  ChallanWebViewBloc? _bloc;
  InAppWebViewController? _webViewController;
  late ChallanWebViewLogic logic;
  bool isPageLoaded = false;

  @override
  void initState() {
    super.initState();
    clearWebViewCookies();
    logic = ChallanWebViewLogic(
        onLoadStopCallback,
        onLoadStartCallback,
        onWebViewCreatedCallback,
        onPageCompleteLoadedCallback,
        shouldOverrideUrlLoadingCallback,
        onCreateWindowCallback,
        onLoadErrorCallback);
    _bloc = BlocProvider.of<ChallanWebViewBloc>(context);
    WebViewRegisters.instance.addRegister(logic);
  }

  @override
  void dispose() {
    super.dispose();
    WebViewRegisters.instance.removeRegister(logic);
  }

  //Reasons for clearing cookies
  //1. clear webview cookies to have a predictable flow
  //2. karnataka website displays an error when routed to homepage directly if cookie has expired.
  void clearWebViewCookies() async {
    CookieManager cookieManager = CookieManager.instance();
    await cookieManager.deleteAllCookies();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ChallanWebViewBloc, ChallanWebViewStates>(
      listenWhen: (previous, current) =>
          current is ExecuteScript ||
          current is ChallanAutomationComplete ||
          current is WebviewLoadUrl ||
          current is LoadTransactionDetailsPage ||
          current is OpenWebviewBottomsheet,
      listener: (BuildContext context, ChallanWebViewStates state) {
        if (state is ExecuteScript) {
          for (var script in state.scripts) {
            AckoLoggerManager.instance.logInfo(
                event: AppDebugInfoEvent(
                    page: 'challan_automation',
                    infoMessage: 'Script Executed',
                    journey: 'challan_automation',
                    data: {
                  "journey": "Challan Automation",
                  "script": script.toString(),
                }));
            _webViewController?.evaluateJavascript(source: script);
          }
        } else if (state is ChallanAutomationComplete) {
          _webViewController?.stopLoading();
          Navigator.pop(context);
        } else if (state is WebviewLoadUrl) {
          _webViewController?.loadUrl(
              urlRequest: URLRequest(url: WebUri(state.url)));
        } else if (state is OpenWebviewBottomsheet) {
          context.showAckoModalBottomSheet(
              child: ChallanWebviewBottomsheet(
            title: state.title,
            subtitle: state.subtitle,
            buttonCta: state.buttonCta,
            imageUrl: state.imageUrl,
          ));
        } else if (state is LoadTransactionDetailsPage) {
          Navigator.pushReplacementNamed(
              context, Routes.CHALLAN_TRANSACTION_PAYMENT_DETAILS,
              arguments: {
                'challanHistoryArgs': ChallanHistoryArgs(
                    noticeNumbers: _bloc?.violationNumbers,
                    registrationNumber: _bloc?.registrationNumber,
                    challanSearchSource: _bloc?.searchSource,
                    shouldRunPaymentUpdateFlow:
                        state.shouldRunPaymentUpdateFlow ?? false,
                    challanAmount:
                        _bloc?.userFlowData.amountBreakup?.totalPayable,
                    shouldOpenReceipt: state.shouldOpenReceipt ?? false,
                    runUpdateFlow: state.runUpdateFlow ?? true),
              });
        }
      },
      buildWhen: (context, state) => (state is ChallanWebViewLoading ||
          state is GetUserInput ||
          state is ChallanWebViewLoaded ||
          state is ChallanError),
      builder: (context, state) {
        return Scaffold(
          resizeToAvoidBottomInset: true,
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            leading: Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: IconButton(
                icon: Align(
                    alignment: Alignment.centerLeft,
                    child: Icon(Icons.close, color: color121212)),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
            ),
          ),
          body: Stack(
            children: [
              Container(
                height: getScreenAwareHeight(300),
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(AssetsConstants.gradient_image),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Offstage(
                offstage: state is! ChallanWebViewLoaded,
                child: AckoWebView(
                  url: widget.sentUrl,
                ),
              ),
              if (state is ChallanWebViewLoading)
                const Center(child: CircularProgressIndicator()),
              if (state is GetUserInput)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: UserInputWidget(
                    challanKeywordModel: state.keywordResponseModel,
                    onSubmit: (userInput) {
                      _bloc?.onUserInputSubmitted(userInput);
                    },
                  ),
                ),
              if (state is ChallanError)
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: ScriptErrorPage(
                    shouldTryAgain: false,
                  ),
                )
            ],
          ),
        );
      },
    );
  }

  onLoadErrorCallback(
      InAppWebViewController controller, Uri? url, int code, String message) {
    AckoLoggerManager.instance.logInfo(
        event: AppDebugInfoEvent(
            page: 'challan_automation',
            infoMessage: 'onLoadErrorCallback fired.',
            journey: 'challan_automation',
            data: {
              "journey": "Challan Automation",
              "code": code.toString(),
              "errorMessage": message,
              "url": url.toString(),
            }));
  }

  onPageCompleteLoadedCallback(InAppWebViewController _controller) async {
    if (isPageLoaded) {
      return;
    }
    isPageLoaded = true;
    Uri? currentUrl = await _webViewController?.getUrl();
    AckoLoggerManager.instance.logInfo(
        event: AppDebugInfoEvent(
            page: 'challan_automation',
            infoMessage: 'User navigated to new page',
            journey: 'challan_automation',
            data: {
          "journey": "Challan Automation",
          "url": currentUrl.toString(),
        }));
    if (await !_bloc?.checkUrlValidity(currentUrl)) {
      return;
    }
    await _bloc?.fetchAutomationResponse(currentUrl);
    await _bloc?.getAutomationScript(
      currentUrl.toString(),
    );
  }

  onLoadStopCallback(Uri? url) async {
    if (_bloc?.userFlowData.userWebviewCookie == null)
      _bloc?.userFlowData.userWebviewCookie =
          await getJSessionID(url.toString());
  }

  onLoadStartCallback(Uri? url) {
    isPageLoaded = false;
  }

  onCreateWindowCallback(
      InAppWebViewController controller, CreateWindowAction request) {
    if (!request.request.url.isNull && Platform.isIOS) {
      controller.loadUrl(urlRequest: URLRequest(url: request.request.url));
    }
    return Right(HandlingVoidMethod());
  }

  Future<Either<NotHandlingMethod, NavigationActionPolicy>>
      shouldOverrideUrlLoadingCallback(NavigationAction action) {
    String url = action.request.url.toString();
    String? urlToOverride = _bloc?.userFlowData.nextUrlOverride;
    bool shouldContinueTransDetailsFlow =
        _bloc?.userFlowData.shouldContinueDetailsFlow ?? false;

    if (urlToOverride != null) {
      _webViewController?.loadUrl(
          urlRequest: URLRequest(url: WebUri(urlToOverride)));
      _bloc?.userFlowData.nextUrlOverride = null;
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (shouldContinueTransDetailsFlow) {
      final uri = Uri.parse(url);
      final updatedUri = uri.replace(queryParameters: {
        ...uri.queryParameters,
        'run-transaction-detail-flow': 'true',
      });
      _webViewController?.loadUrl(
        urlRequest: URLRequest(url: WebUri(updatedUri.toString())),
      );
      _bloc?.userFlowData.shouldContinueDetailsFlow = false;
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    }
    _webViewController?.goForward();
    return Future.value(Right(NavigationActionPolicy.ALLOW));
  }

  onWebViewCreatedCallback(InAppWebViewController controller) async {
    AnalyticsTrackerManager.instance.sendEvent(
        event: CardLoadedConstants.TRACK_EVENT_COMPLETE,
        properties: {
          'journey': "Challan Automation",
          'name': "Challan Webview Initialised",
          'product': _bloc?.registrationNumber,
          'source': _bloc?.searchSource,
        });
    AckoLoggerManager.instance.logInfo(
        event: AppDebugInfoEvent(
            page: 'challan_automation',
            infoMessage: 'Challan Webview created',
            journey: 'challan_automation',
            data: {
          "journey": "Challan Automation",
        }));
    _webViewController = controller;
    _webViewController?.addJavaScriptHandler(
        handlerName: 'jsHandler',
        callback: (args) {
          if (args.isEmpty) {
            return null;
          }
          final data = Map<String, dynamic>.from(args[0]);
          ChallanKeywordModel model = ChallanKeywordModel.fromJson(data);
          return _bloc?.askUserInput(model);
        });
  }

  Future<String?> getJSessionID(String urlString) async {
    CookieManager cookieManager = CookieManager.instance();
    List<Cookie> cookies =
        await cookieManager.getCookies(url: WebUri(urlString));

    for (var cookie in cookies) {
      if (cookie.name == "JSESSIONID") {
        return cookie.value;
      }
    }
    return null;
  }
}
