import 'dart:convert';
import 'dart:ui';

import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/challan_webview/enums/challan_webview_enums.dart';
import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

import '../../common/util/PreferenceHelper.dart';
import '../challan/challan_enums.dart';
import 'utils/challan_webview_utility.dart';
import 'challan_automation_repository.dart';
import 'models/challan_automation_misc_models.dart';
import 'models/ka_challan_history_model.dart';
import 'models/ka_payment_status_ingestion_model.dart';
import 'models/user_challan_order_history.dart';

class ChallanTransactionDetailBloc
    extends AckoSafeCubit<ChallanTransactionDetailStates> {
  ChallanHistoryArgs historyArgs;

  ChallanTransactionDetailBloc({required this.historyArgs})
      : super(ChallanTransactionDetailLoading()) {
    checkPaymentStatus();
  }

  final _automationRepo = ChallanAutomationRepository();

  checkPaymentStatus() async {
    if (!_areRequiredFieldsValid()) {
      emit(ChallanTransactionDetailError());
      return;
    }

    final userPaymentStatus = await _getUserPaymentStatus();
    if (userPaymentStatus == null) {
      emit(ChallanTransactionDetailError());
      return;
    }

    if (userPaymentStatus.paymentStatus ==
        ChallanPaymentStatus.PAYMENT_INITIATED) {
      _handlePaymentInitiated(userPaymentStatus);
      return;
    }

    await _handleCompletedPaymentFlow();
  }

  bool _areRequiredFieldsValid() {
    return historyArgs.noticeNumbers.isNotNullOrEmpty &&
        historyArgs.registrationNumber.isNotNullOrEmpty &&
        historyArgs.challanSearchSource != null;
  }

  Future<ChallanAutomationPaymentStatus?> _getUserPaymentStatus() async {
    final userPaymentStatus = await _automationRepo.getUserPaymentStatus(
        historyArgs.noticeNumbers!,
        historyArgs.registrationNumber!,
        historyArgs.challanSearchSource!);

    if (userPaymentStatus.error.isNotNullOrEmpty) {
      return null;
    }

    return userPaymentStatus;
  }

  void _handlePaymentInitiated(
      ChallanAutomationPaymentStatus userPaymentStatus) {
    final searchSource = ChallanSearchSource.forAutomationFromString(
        historyArgs.challanSearchSource!);
    executePaymentInitiationFlow(userPaymentStatus, searchSource);
  }

  Future<void> _handleCompletedPaymentFlow() async {
    final parsedSearchSource = ChallanSearchSource.forAutomationFromString(
        historyArgs.challanSearchSource!);

    final orderHistory = await _getChallanOrderHistory(parsedSearchSource,
        historyArgs.noticeNumbers!, historyArgs.registrationNumber!);

    if (orderHistory == null) {
      emit(ChallanTransactionDetailError());
      return;
    }

    if (_isPaymentCompleted(orderHistory)) {
      constructChallanPaymentDetails(challanDetailsModel: orderHistory);
    } else {
      emit(ChallanTransactionDetailError());
    }
  }

  bool _isPaymentCompleted(dynamic orderHistory) {
    return orderHistory.ackoPaymentStatus ==
            ChallanPaymentStatus.PAYMENT_FAILED ||
        orderHistory.ackoPaymentStatus == ChallanPaymentStatus.PAYMENT_SUCCESS;
  }

  Color _getPaymentStatusColor(ChallanPaymentStatus paymentStatus) {
    return switch (paymentStatus) {
      ChallanPaymentStatus.PAYMENT_SUCCESS => color0FA457,
      ChallanPaymentStatus.PAYMENT_FAILED => colorEE1520,
      _ => color121212,
    };
  }

  executePaymentInitiationFlow(ChallanAutomationPaymentStatus userPaymentStatus,
      ChallanSearchSource source) async {
    String? userPhoneNumber =
        await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);

    if (source == ChallanSearchSource.KA) {
      UserChallanOrderHistory? challanHistoryData;

      final karnatakaChallanStatus =
          await _getKaChallanStatus(source, userPaymentStatus.referenceNumber!);

      if (karnatakaChallanStatus?.error == null) {
        final historyData = UserChallanOrderHistory.fromKarnatakaChallanStatus(
            karnatakaChallanStatus!, historyArgs.challanNumbers ?? null);
        constructChallanPaymentDetails(challanDetailsModel: historyData);
        return;
      } else {
        final String? urlToLoad = getReloginUrl();
        if (urlToLoad == null) {
          emit(ChallanTransactionDetailError());
          return;
        }
        emit(LoadChallanWebview(
            url: urlToLoad, userPhoneNumber: userPhoneNumber!));
      }
    } else if (source == ChallanSearchSource.TS) {
      String? url = getReloginUrl();
      if (url.isNullOrEmpty) {
        emit(ChallanTransactionDetailError());
        return;
      }
      if (historyArgs.shouldRunPaymentUpdateFlow == true) {
        String updatedUrl = ChallanWebviewUtility.appendFragmentToUrl(url!,
            'payment_reference_number', userPaymentStatus.referenceNumber!);
        updatedUrl = (historyArgs.challanAmount.isNotNullOrEmpty)
            ? ChallanWebviewUtility.appendFragmentToUrl(
                updatedUrl, 'challan_amount', historyArgs.challanAmount!)
            : updatedUrl;
        emit(LoadChallanWebview(
            url: updatedUrl, userPhoneNumber: userPhoneNumber!));
        return;
      }
      constructChallanPaymentDetails(
          challanDetailsModel: UserChallanOrderHistory.fromChallanHistoryArgs(
              paymentStatus: userPaymentStatus.paymentStatus!,
              registrationNumber: userPaymentStatus.registrationNumber!,
              referenceNumber: userPaymentStatus.referenceNumber!,
              noticeNumbers: [userPaymentStatus.noticeNumber!],
              challanAmount: historyArgs.challanAmount));
    } else if (source == ChallanSearchSource.MH) {
      if (historyArgs.runUpdateFlow != null && !historyArgs.runUpdateFlow!) {
        constructChallanPaymentDetails(
            challanDetailsModel: UserChallanOrderHistory.fromChallanHistoryArgs(
                paymentStatus: userPaymentStatus.paymentStatus!,
                registrationNumber: userPaymentStatus.registrationNumber!,
                referenceNumber: userPaymentStatus.referenceNumber!,
                noticeNumbers: [userPaymentStatus.noticeNumber!],
                challanAmount: historyArgs.challanAmount));
        return;
      }
      String? url = getReloginUrl();
      if (url.isNullOrEmpty) {
        emit(ChallanTransactionDetailError());
        return;
      }
      final updatedUrl = ChallanWebviewUtility.constructChallanUrl(
          url!,
          [
            UrlAppendAction.paymentUpdateFlow,
            UrlAppendAction.paymentReferenceNumber
          ],
          referenceNumber: userPaymentStatus.referenceNumber!);
      emit(LoadChallanWebview(
          url: updatedUrl, userPhoneNumber: userPhoneNumber!));
    } else {
      emit(ChallanTransactionDetailError());
    }
  }

  Future<UserChallanOrderHistory?>? _getChallanOrderHistory(
      ChallanSearchSource source,
      List<String> noticeNumbers,
      String registrationNumber) async {
    final orderHistory = await _automationRepo.getUserChallanOrderHistory(
      noticeNumbers,
      registrationNumber,
      source.name,
    );
    if (orderHistory.error == null) {
      return orderHistory;
    }
    return null;
  }

  Future<KaChallanHistoryModel?>? _getKaChallanStatus(
      ChallanSearchSource source, String referenceNumber) async {
    final userIdData = await _automationRepo.getUserIdValue(source.name);
    final singleOrderHistory = await _automationRepo.checkStatusOfChallan(
      referenceNumber: referenceNumber,
      userIdValue: userIdData.sessionId,
      source: source,
    );

    if (singleOrderHistory.error == null) {
      String? userPhoneNumber =
          await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);
      sendPaymentStatusForIngestion(
        paymentStatusModel: singleOrderHistory,
        phoneNumber: userPhoneNumber ?? '',
        paymentStatus: singleOrderHistory.ackoPaymentStatus,
      );
      return singleOrderHistory;
    }
    return null;
  }

  sendPaymentStatusForIngestion(
      {KaChallanHistoryModel? paymentStatusModel,
      required ChallanPaymentStatus paymentStatus,
      required String phoneNumber}) {
    // Challan history is only sent when payment is either successful/failure,
    // if it's anything other than these, we won't have the necessary data to send the history properties
    bool shouldSendHistoryProps =
        (paymentStatus == ChallanPaymentStatus.PAYMENT_FAILED ||
            paymentStatus == ChallanPaymentStatus.PAYMENT_SUCCESS);
    String? receiptUrl;

    if (paymentStatus == ChallanPaymentStatus.PAYMENT_SUCCESS &&
        historyArgs.noticeNumbers != null &&
        paymentStatusModel?.deptRefNum != null &&
        historyArgs.challanSearchSource == ChallanSearchSource.KA.name) {
      receiptUrl = ChallanAutomationConstants.getKaChallanReceiptUrl(
          historyArgs.noticeNumbers![0], paymentStatusModel!.deptRefNum!);
    }
    _automationRepo.sendPaymentStatus(
      noticeNumbers: historyArgs.noticeNumbers ?? [],
      phoneNumber: phoneNumber,
      referenceNumber: paymentStatusModel?.deptRefNum ?? '',
      registrationNumber: paymentStatusModel?.vehicleNumber ?? '',
      source: ChallanSearchSource.forAutomationFromString(
          historyArgs.challanSearchSource!),
      ackoPaymentStatus: paymentStatus,
      paymentStatus: paymentStatusModel?.paymentStatus,
      challanAmount: paymentStatusModel?.challanAmount.toString(),
      shouldSendHistroyProps: shouldSendHistoryProps,
      receiptUrl: receiptUrl,
    );
  }

  String? getReloginUrl() {
    final reloginUrlsFromFirebase = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.challanWebviewReloginUrls);

    final Map<String, dynamic>? data = jsonDecode(reloginUrlsFromFirebase);

    final keyToSearch = historyArgs.challanSearchSource;
    return data?[keyToSearch];
  }

  constructChallanPaymentDetails(
      {required UserChallanOrderHistory challanDetailsModel}) async {
    emit(ChallanTransactionDetailLoading());

    AnalyticsTrackerManager.instance
        .sendEvent(event: PageLoadedConstants.APP_VIEW_PAGE, properties: {
      "page_name": "challan transaction details",
      "status": challanDetailsModel.ackoPaymentStatus?.name,
    });

    final details = <ChallanTransactionDetailRow>[
      if (challanDetailsModel.challanAmount.isNotNullOrEmpty)
        ChallanTransactionDetailRow(
          label: ChallanAutomationConstants.amountPaid,
          value: ChallanAutomationConstants.rupeeSymbol +
              challanDetailsModel.challanAmount!,
          valueColor: color121212,
          isBold: true,
        ),
      if (challanDetailsModel.ackoPaymentStatus != null)
        ChallanTransactionDetailRow(
          label: ChallanAutomationConstants.paymentStatusString,
          value: ChallanWebviewUtility.mapPaymentStatusToString(
              challanDetailsModel.ackoPaymentStatus!),
          valueColor:
              _getPaymentStatusColor(challanDetailsModel.ackoPaymentStatus!),
          isBold: false,
        ),
      if (challanDetailsModel.registrationNumber.isNotNullOrEmpty)
        ChallanTransactionDetailRow(
          label: ChallanAutomationConstants.registrationNumberString,
          value: challanDetailsModel.registrationNumber!,
          valueColor: color121212,
          isBold: false,
        ),
      if (challanDetailsModel.challanNumbers.isNotNullOrEmpty &&
          challanDetailsModel.challanNumbers![0].violation.isNotNullOrEmpty)
        ChallanWebviewUtility.getViolationDetails(
            challanDetailsModel.challanNumbers!),
      if (challanDetailsModel.refNumber.isNotNullOrEmpty)
        ChallanTransactionDetailRow(
          label: ChallanAutomationConstants.transactionIdString,
          value: challanDetailsModel.refNumber!,
          valueColor: color121212,
          isBold: false,
        ),
    ];

    emit(ChallanTransactionDetailLoaded(
        challanDetails: details,
        challanHistoryResponse: challanDetailsModel,
        isDownloadReceiptAvailable:
            isDownloadReceiptAvailable(challanDetailsModel)));
  }

  loadChallanWebview(String url, String userPhoneNumber) {
    emit(LoadChallanWebview(url: url, userPhoneNumber: userPhoneNumber));
  }

  isDownloadReceiptAvailable(UserChallanOrderHistory challanDetailsModel) {
    if (challanDetailsModel.ackoPaymentStatus ==
        ChallanPaymentStatus.PAYMENT_FAILED) {
      return false;
    } else if (challanDetailsModel.ackoPaymentStatus ==
        ChallanPaymentStatus.PAYMENT_INITIATED) {
      return false;
    } else if (challanDetailsModel.receiptUrl.isNotNullOrEmpty) {
      return true;
    }
    if (historyArgs.challanSearchSource == ChallanSearchSource.MH.name) {
      String? reloginUrl = getReloginUrl() ?? '';
      if (reloginUrl.isNullOrEmpty) {
        return DownloadReceiptAvailability(isAvailable: false);
      } else {
        return true;
      }
    }
    return false;
  }
}

abstract class ChallanTransactionDetailStates {}

class ChallanTransactionDetailLoading extends ChallanTransactionDetailStates {}

class ChallanTransactionDetailLoaded extends ChallanTransactionDetailStates {
  final challanDetails;
  final UserChallanOrderHistory challanHistoryResponse;
  final bool isDownloadReceiptAvailable;
  ChallanTransactionDetailLoaded(
      {this.challanDetails,
      required this.challanHistoryResponse,
      required this.isDownloadReceiptAvailable});
}

class ChallanTransactionDetailError extends ChallanTransactionDetailStates {}

class LoadChallanWebview extends ChallanTransactionDetailStates {
  final String url;
  final String userPhoneNumber;

  LoadChallanWebview({required this.url, required this.userPhoneNumber});
}
