import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/challan/challan_enums.dart';
import 'package:acko_flutter/feature/challan_sdui/model/challan_transaction_model.dart';
import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:acko_logger/acko_logger.dart';
import 'package:acko_logger/events/info/app_debug_info.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/card_loaded_events.dart';
import 'package:utilities/constants/constants.dart';
import 'package:acko_flutter/util/Utility.dart';

import '../../../util/extensions.dart';
import '../enums/challan_webview_enums.dart';
import '../models/challan_automation_misc_models.dart';
import '../models/challan_keyword_model.dart';

class ChallanWebviewUtility {
  static TransactionStatusModel getTransactionStatusConstants(
      ChallanPaymentStatus statusText, ChallanSearchSource searchSource) {
    if (statusText == ChallanPaymentStatus.PAYMENT_FAILED) {
      return TransactionStatusModel(
          title: payment_failed,
          subtitle: ChallanAutomationConstants.getChallanPaymentFailureMessage(
              mapSearchSourceToString(searchSource)),
          transactionImage: AssetsConstants.failed_anim);
    }
    return TransactionStatusModel(
        title: ChallanAutomationConstants.paymentCompleted,
        subtitle: ChallanAutomationConstants.challanPaymentSuccessMessage,
        transactionImage: AssetsConstants.success_anim);
  }

  static String mapPaymentStatusToString(ChallanPaymentStatus statusText) {
    if (statusText == ChallanPaymentStatus.PAYMENT_INITIATED) {
      return ChallanAutomationConstants.payment_initiated;
    } else if (statusText == ChallanPaymentStatus.PAYMENT_FAILED) {
      return ChallanAutomationConstants.payment_failed;
    } else if (statusText == ChallanPaymentStatus.PAYMENT_SUCCESS) {
      return ChallanAutomationConstants.payment_success;
    }
    return "";
  }

  static String mapSearchSourceToString(ChallanSearchSource searchSource) {
    if (searchSource == ChallanSearchSource.TS) {
      return ChallanAutomationConstants.telanganaRto;
    } else if (searchSource == ChallanSearchSource.MH) {
      return ChallanAutomationConstants.maharashtraRto;
    }
    return ChallanAutomationConstants.karnatakaRto;
  }

  static String extractNameFromUpiID(String upiId) {
    return upiId.contains('@') ? upiId.split('@')[0] : " ";
  }

  static String appendFragmentToUrl(String url, String key, String value) {
    final newUri = Uri.parse(url);
    final frag = newUri.fragment;
    final pairs = <String>[];
    if (frag.isNotEmpty) pairs.addAll(frag.split('&'));
    pairs.add('$key=$value');
    return newUri.replace(fragment: pairs.join('&')).toString();
  }

  static ChallanPaymentStatus mapStringToPaymentStatus(String statusText) {
    if (statusText.equalsIgnoreCase(ChallanPaymentStatus.PAYMENT_FAILED.name)) {
      return ChallanPaymentStatus.PAYMENT_FAILED;
    } else if (statusText
        .equalsIgnoreCase(ChallanPaymentStatus.PAYMENT_SUCCESS.name)) {
      return ChallanPaymentStatus.PAYMENT_SUCCESS;
    } else if (statusText
        .equalsIgnoreCase(ChallanPaymentStatus.PAYMENT_PENDING.name)) {
      return ChallanPaymentStatus.PAYMENT_PENDING;
    } else {
      return ChallanPaymentStatus.PAYMENT_INITIATED;
    }
  }

  static String getReceiptFileExtension(String? searchSource) {
    ChallanSearchSource source =
        ChallanSearchSource.forAutomationFromString(searchSource!);
    if (source == ChallanSearchSource.TS || source == ChallanSearchSource.MH) {
      return ChallanAutomationConstants.jpegExtension;
    }
    return ChallanAutomationConstants.pdfExtension;
  }

  static bool isBackgroundTask(KeywordResponseType responseType) {
    if (responseType == KeywordResponseType.skip_page ||
        responseType == KeywordResponseType.open_bottomsheet ||
        responseType == KeywordResponseType.automation_logging ||
        responseType == KeywordResponseType.save_amount_breakup) return true;
    return false;
  }

  static ChallanTransactionDetailRow getViolationDetails(
      List<ChallanNumberData> challanNumbers) {
    final List<Map<String, String>> violationList = [];
    for (final cn in challanNumbers) {
      for (final v in cn.violation) {
        violationList.add({
          v.offence: ChallanAutomationConstants.rupeeSymbol +
              int.parse(v.amount).formatPriceWithCommas()
        });
      }
    }

    final isSingleViolation = violationList.length == 1;

    return ChallanTransactionDetailRow(
        label: 'Challan details',
        value: isSingleViolation
            ? "${violationList.first.keys.first}"
            : "${violationList.length} challans",
        openBottomsheet: !isSingleViolation,
        detailItems: isSingleViolation ? null : violationList,
        isBold: false,
        valueColor: color121212);
  }

  static ErrorScreenConstants getErrorScreenConstants(bool shouldTryAgain) {
    if (shouldTryAgain) {
      return ErrorScreenConstants(
          mainImage: ChallanAutomationConstants.imageUnexpectedError,
          title: ChallanAutomationConstants.thisWasUnexpected,
          subtitle: ChallanAutomationConstants.tryAutomationRequestLater,
          buttonText: ChallanAutomationConstants.tryAgain);
    } else {
      return ErrorScreenConstants(
          mainImage: ChallanAutomationConstants.imageSomethingWentWrong,
          title: ChallanAutomationConstants.somethingWentWrong,
          subtitle: ChallanAutomationConstants.unableToCompleteRequest,
          buttonText: ChallanAutomationConstants.okay);
    }
  }

  static TrackingScreenRowModel getChallanTrackingScreenConstants(
      TrackingScreenStateType stateType) {
    return switch (stateType) {
      TrackingScreenStateType.loading => TrackingScreenRowModel(
          textColor: color121212, stateImage: AssetsConstants.empty_black_tick),
      TrackingScreenStateType.loaded => TrackingScreenRowModel(
          textColor: color121212,
          stateImage: AssetsConstants.filled_black_tick),
      _ => TrackingScreenRowModel(
          textColor: color4B4B4B, stateImage: AssetsConstants.empty_black_tick),
    };
  }

  static sendChallanAutomationAnalyticEvent(
      {required ChallanKeywordModel model,
      String source = "KA",
      String? userInput}) {
    String? name;

    if (model.responseType == KeywordResponseType.otp_field) {
      name = ChallanAutomationConstants.otpEntered;
    } else if (model.responseType == KeywordResponseType.upi_in_progress) {
      name = ChallanAutomationConstants.upiRequestSent;
    } else if (model.responseType == KeywordResponseType.final_screen) {
      name = ChallanAutomationConstants.finalScreen;
    } else if (model.responseType == KeywordResponseType.pay_through_upi) {
      name = ChallanAutomationConstants.upiIdEntered;
    }

    AnalyticsTrackerManager.instance.sendEvent(
        event: CardLoadedConstants.TRACK_EVENT_COMPLETE,
        properties: {
          'journey': "Challan Automation",
          'name': name,
          'product': userInput,
          'source': source
        });
  }

  static logChallanAutomationEvents(AutomationLoggingProperties loggingProps) {
    AckoLoggerManager.instance.logInfo(
        event: AppDebugInfoEvent(
            page: 'challan_automation',
            infoMessage: loggingProps.message,
            journey: 'challan_automation',
            data: loggingProps.metadata));
  }

  static String constructChallanUrl(
    String url,
    List<UrlAppendAction> actions, {
    String? referenceNumber,
    String? challanAmount,
  }) {
    final Map<String, String> params = <String, String>{};
    if (actions.isEmpty) return url;

    for (final action in actions) {
      switch (action) {
        case UrlAppendAction.receiptFlow:
          params['get-receipt-flow'] = 'true';
          break;
        case UrlAppendAction.shouldOpenReceipt:
          params['should_open_receipt'] = 'true';
          break;
        case UrlAppendAction.paymentReferenceNumber:
          if (referenceNumber != null) {
            params['payment-reference-number'] = referenceNumber;
          }
          break;
        case UrlAppendAction.paymentUpdateFlow:
          params['update-payment-status'] = 'true';
          break;
        case UrlAppendAction.hideWebviewAppbar:
          params['hide_app_bar'] = 'true';
          break;
      }
    }

    if (params.isEmpty) return url;

    final Uri uri = Uri.parse(url);
    final bool useFragment = url.trim().endsWith('/');

    return useFragment
        ? Util.buildUrlWithFragment(uri, params)
        : Util.buildUrlWithQueryParams(uri, params);
  }
}
