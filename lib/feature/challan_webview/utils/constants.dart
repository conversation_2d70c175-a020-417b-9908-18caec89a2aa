import 'dart:io';

import 'package:acko_flutter/feature/challan/challan_enums.dart';

class ChallanAutomationConstants {
  static const String addUpiId = 'Add new UPI ID';
  static const String payNow = 'Pay now';
  static const String enterUpiId = 'Enter your UPI ID';
  static const String saveMyUpiId = 'Save my UPI ID for future payments';
  static const String yourPaymentHasInitiated = 'Track your payment status ➜';
  static const String payment_initiated = 'Initiated';
  static const String payment_success = 'Successful';
  static const String challanPaymentSuccessMessage =
      'It usually takes up to 24 hours for the RTO to process payments';
  static const String payment_failed = 'Failed';
  static const String paymentPendingAutomation = 'Your payment is pending';
  static const String paymentSuccessfulCheckDetails =
      'View your payment details ➜';
  static const String paymentFailedCheckDetails =
      'Check your failed payment details ➜ ';
  static const String rupeeSymbol = '\u{20B9}';
  static const String getOtp = 'Get OTP';
  static const String redirectedToGovernmentWebsite =
      "You will be redirected to the government's website";
  static const String directlyPayingTheGovernment =
      'You will be paying directly to the government';
  static const String paymentCompleted = 'Payment completed';
  static const String otpEntered = 'OTP entered';
  static const String upiRequestSent = 'UPI request sent';
  static const String finalScreen = 'Final Screen';
  static const String upiIdEntered = 'UPI Id entered';
  static const String somethingWentWrong = 'Something went wrong';
  static const String tryAgain = 'Try Again';
  static const String unableToCompleteRequest =
      'We’re unable to complete your request. Please try again after some time.';
  static const String statusCouldNotBeRefereshed =
      'Status could not be refreshed';
  static const String takesTwentyFourHoursForRtoPayment =
      'It usually takes up to 24 hours for the RTO to process payments.';
  static const String processingYourPayment = 'Processing your payment';
  static const String pleaseDoNotGoBackOrCloseApp =
      'Please do not go back or close the app';
  static const String cancelPayment = 'Cancel Payment';
  static const String minsRemaining = 'mins remaining';
  static const String payUsingUpiID = 'Pay using UPI';
  static const String paymentDetails = 'Payment Details';
  static const String fetchingChallanDetails = 'Fetching challan details...';
  static const String resendOtp = 'Resend OTP';
  static const String submit = 'Submit';
  static const String no = 'No';
  static const String okay = 'Okay';
  static const String tryAutomationRequestLater =
      'We’re unable to complete your request. Please try again.';
  static const String yesCancel = 'Yes, cancel';
  static const String downloadReceipt = 'Download receipt';
  static const String continueWord = 'Continue';
  static const String areYouSureYouWantToCancel =
      'Are you sure you want to cancel?';
  static const String pendingChallanConsequences =
      'Having pending challans could result in unnecessary legal troubles';
  static const String thisWasUnexpected = 'Well, this is unexpected.';
  static const rtoFlowPaymentProcessingMessage =
      'You will directly pay the Maharashtra RTO through ACKO';
  static const String karnatakaRto = 'Karnataka';
  static const String telanganaRto = 'Telangana';
  static const String maharashtraRto = 'Maharashtra';
  static const String didYouCompletePayment =
      'Were you able to complete your challan payment?';
  static const String weNeedToUpdateStatus =
      "We need this to update your challan’s status";
  static const String imageTrackingScreenEvaluation =
      'https://auto.ackoassets.com/central-app/features/asset_v9/Processing_opt.json';
  static const String imageSomethingWentWrong =
      'https://auto.ackoassets.com/central-app/features/asset_v9/Error_3.svg';
  static const String imageUnexpectedError =
      'https://auto.ackoassets.com/central-app/features/asset_v9/Error_2.svg';
  static const String imageDocumentMissing =
      'https://auto.ackoassets.com/central-app/features/app_v9/common/ic_doc_miss.svg';
  static const String imageDownloadIcon =
      'https://auto.ackoassets.com/central-app/features/fastag-ux/download-icon.svg';
  static const String amountPaid = 'Amount paid';
  static const String paymentStatusString = 'Payment status';
  static const String registrationNumberString = 'Registration number';
  static const String transactionIdString = 'Transaction ID';
  static const String somethingNotRight = 'Something not right?';
  static const String paymentMadeDirectlyToRto =
      'Since payments are directly made to the RTO, please reach out to them for any issues.';
  static const String secureDataPassingMessage =
      "We’ll securely pass your details to the RTO to complete the payment.";
  static const String jpegExtension = '.jpeg';
  static const String pdfExtension = '.pdf';
  static const String replacementTextRegistrationNumber =
      'parameter_registrationNumber';
  static const String replacementTextViolationNumbers =
      'parameter_violationNumbers';
  static const String replacementTextPhoneNumber = 'parameter_phoneNumber';

  static const String sendUpiRequestUrl = '/vas/api/v1/challans/create-order/';
  static const String checkUpiRequestStatusUrl =
      '/vas/api/v1/challans/query-order/';
  static const String getUserUpiIdDetails =
      '/authorization/api/auth/user/get?bank_details=true';
  static const String getStartChallanCronJobUrl =
      '/vas/api/v1/challans/payment/publish';
  static const String getChallanUserIdValueUrl =
      '/vas/api/v1/challans/user-session';
  static const String sendUserChallanStatusUrl =
      '/vas/api/v1/challans/payment-details';

  static const Map<ChallanUpiProvider, String> providerTitleMap = {
    ChallanUpiProvider.paytm: 'Paytm',
    ChallanUpiProvider.googlePay: 'Google Pay',
    ChallanUpiProvider.phonePe: 'PhonePe',
    ChallanUpiProvider.amazonPay: 'Amazon Pay',
    ChallanUpiProvider.cred: 'Cred',
  };

  static String payingRTO(String rtoName) =>
      'You will directly pay the ${rtoName} RTO through ACKO';

  static String buildUpiPaymentMessage(String upiProvider) =>
      'Open ${upiProvider} and complete the payment request by the RTO';

  static String generateOtpInstruction(String phoneNumber) =>
      'The RTO will send you an OTP on ${phoneNumber}';

  static String numberedChallanMessage(int numOfChallans) =>
      '$numOfChallans challan${numOfChallans == 1 ? '' : 's'} • ';

  static String getChallanPaymentFailureMessage(String searchSource) =>
      'This usually does not happen. If any amount is debited, please contact the ${searchSource} RTO.';

  static String getChallanReceiptFilePath(
          Directory directory, String fileExtension) =>
      '${directory.path}/challan_receipt_${DateTime.now().millisecondsSinceEpoch}${fileExtension}';

  static String saveUpiIdUrl(String userUpiId, String accountHolder) =>
      '/auto-asset/save-upi-id?user_upi_id=${userUpiId}&account_holder=${accountHolder}';

  static String getKaChallanReceiptUrl(
          String noticeNumber, String referenceNumber) =>
      "https://kspapp.ksp.gov.in/ksp/api/traffic-challan/download-receipt-web?noticeNumber=${noticeNumber}&deptRefNum=${referenceNumber}";

  static String getCreateCaptchaUrl(String source) =>
      '/auto-asset/create-captcha-task?searchSource=${source}';

  static String getCaptchaResultUrl(int taskId) =>
      '/auto-asset/get-captcha-result?taskId=${taskId}';

  static String getKaPaymentUrl(
      String searchSource, String userIdValue, List<String> noticeNumbers) {
    final csv = noticeNumbers.join(',');
    return '/vas/api/v1/challans/payment-link'
        '?source=${searchSource}'
        '&session-id=${userIdValue}'
        '&notice-number=${csv}';
  }

  static String getDefaultChallanAutomationStatus() {
    return """
        {
          "ka": {
            "redirection_url": "https://kspapp.ksp.gov.in/ksp/api/traffic-challan",
            "is_automation_active": "false",
            "ios_min_build_number": "999",
            "android_min_build_number": "999"
          },
          "parivahan": {
            "redirection_url": "https://echallan.parivahan.gov.in/index/accused-challan",
            "is_automation_active": "false",
            "ios_min_build_number": "999",
            "android_min_build_number": "999"
          },
          "mh": {
            "redirection_url": "https://mahatrafficechallan.gov.in/payechallan/PaymentService.htm",
            "is_automation_active": "false",
            "ios_min_build_number": "999",
            "android_min_build_number": "999"
          },
          "dl": {
            "redirection_url": "https://traffic.delhipolice.gov.in/notice/pay-notice",
            "is_automation_active": "false",
            "ios_min_build_number": "999",
            "android_min_build_number": "999"
          },
          "delhi traffic police": {
            "redirection_url": "https://traffic.delhipolice.gov.in/notice/pay-notice",
            "is_automation_active": "false",
            "ios_min_build_number": "999",
            "android_min_build_number": "999"
          },
          "ts": {
            "redirection_url": "https://echallan.tspolice.gov.in/publicview/",
            "is_automation_active": "false",
            "ios_min_build_number": "999",
            "android_min_build_number": "999"
          }
        }
        """;
  }
}
