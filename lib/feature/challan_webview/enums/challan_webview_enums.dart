enum QuestionKeywords {
  trigger_otp,
  trigger_phone,
  trigger_email,
  trigger_payment_mode_KA,
  trigger_aggregator_type_KA,
  trigger_direct_integration_KA,
}

enum KeywordResponseType {
  textfield,
  otp_field,
  upi_in_progress,
  upi_completed,
  final_screen,
  error,
  pay_through_upi,
  skip_page,
  custom_loading_screen,
  tracking_loading_screen,
  automation_logging,
  solve_captcha,
  script_error,
  timer_loading_screen,
  open_bottomsheet,
  save_amount_breakup,
  show_info,
}

enum SkipType {
  skip_to_ka_payment,
  skip_to_transaction_details_page,
  start_cron_job,
  save_upi_id,
  save_reference_number,
  update_payment_details,
  save_mh_payment_details,
  send_upi_request,
  check_mh_payment_status,
  show_webview,
  hide_webview,
  set_flow_direction,
  check_flow_direction,
  save_payment_receipt,
  no_skip
}

enum CaptchaProgressType {
  processing,
  ready,
  unknown,
}

enum ChallanLoggingType { log_info, log_error }


enum ScriptErrorType {
  reload_page,
  navigate_to_home,
}

enum TrackingScreenStateType {
  loaded,
  loading,
  not_started,
}

enum UpiStatus { pending, successful, failed }

enum HelperScript {
  navigate_to_home,
  resend_otp,
  reload_page,
}

enum ChallanWebviewActions { complete_automation, resume_automation }

enum ChallanWebviewLoadingType {
  threeDotLoader,
  defaultLoader
}

enum UrlAppendAction {
  receiptFlow,
  paymentUpdateFlow,
  shouldOpenReceipt,
  paymentReferenceNumber,
  hideWebviewAppbar
}