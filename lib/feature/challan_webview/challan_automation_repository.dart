import 'dart:convert';

import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:acko_logger/acko_logger.dart';
import 'package:acko_logger/events/error/api_error.dart';
import 'package:acko_logger/events/info/api_response_info.dart';
import 'package:dio/dio.dart';
import 'package:networking_module/networking_module.dart';
import 'package:utilities/constants/constants.dart';

import '../challan/challan_enums.dart';
import 'models/challan_automation_misc_models.dart';
import 'models/ka_challan_history_model.dart';
import 'models/ka_payment_status_ingestion_model.dart';
import 'models/ka_payment_url_model.dart';
import 'models/ka_user_id_model.dart';
import 'package:http_parser/http_parser.dart' show MediaType;
import 'models/user_challan_order_history.dart';
import 'models/user_upi_id_model.dart';

class ChallanAutomationRepository {
  Future<UserUpiIdModel>? getUsersUpiIds(
      ChallanSearchSource searchSource) async {
    final _restService = networkRestService;
    final result = await _restService.apiCall(
      ApiRequest(
          apiPath: ChallanAutomationConstants.getUserUpiIdDetails, requestType: RequestType.get),
    );
    return result.response.fold(
      (failure) {
        AckoLoggerManager.instance.logError(
            event: ApiErrorEvent(
                errorMessage: 'challan_automation_get_users_upi_ids_failed',
                page: Routes.CHALLAN_WEB_PAGE,
                path: ChallanAutomationConstants.getUserUpiIdDetails,
                type: 'GET',
                data: {"source": searchSource.name},
                errorResponse: jsonEncode({'data' : failure.error.toString()})
            )
        );
        return UserUpiIdModel.error("Error Occurred");
      },
      (success) {
        AckoLoggerManager.instance.logInfo(event: ApiResponseInfoEvent(
            page: Routes.CHALLAN_WEB_PAGE,
            path: ChallanAutomationConstants.getUserUpiIdDetails,
            statusCode: success.statusCode,
            data: {"source": searchSource.name},
            response: success.data.toString())
        );
        return UserUpiIdModel.fromJson(success.data);
      },
    );
  }

  Future<KaPaymentUrlModel> getKaPaymentUrl(String email, List<String> noticeNumbers,
      String? userIdValue, ChallanSearchSource searchSource) async {
    final _restService = networkRestService;

    final result = await _restService.apiCall(
      ApiRequest(
          apiPath: ChallanAutomationConstants.getKaPaymentUrl(searchSource.name, userIdValue!, noticeNumbers),
          requestType: RequestType.get),

    );
    return result.response.fold(
      (failure) {
        AckoLoggerManager.instance.logError(
            event: ApiErrorEvent(
                errorMessage: 'challan_automation_get_ka_payment_url_failed',
                page: Routes.CHALLAN_WEB_PAGE,
                path: ChallanAutomationConstants.getKaPaymentUrl(searchSource.name, userIdValue, noticeNumbers),
                type: 'GET',
                data: {"source": searchSource.name},
                arguments: {
                  "email": email,
                  "notice_number": noticeNumbers,
                  "USER_ID_VALUE": userIdValue,
                },
                errorResponse: jsonEncode({'data' : failure.error.toString()})
            )
        );
        return KaPaymentUrlModel.error("Error Occurred");
      },
      (success) {
        AckoLoggerManager.instance.logInfo(event: ApiResponseInfoEvent(
            page: Routes.CHALLAN_WEB_PAGE,
            path: ChallanAutomationConstants.getKaPaymentUrl(searchSource.name, userIdValue, noticeNumbers),
            statusCode: success.statusCode,
            data: {"source": searchSource.name},
            arguments: {
              "email": email,
              "notice_number": noticeNumbers,
              "USER_ID_VALUE": userIdValue,
            },
            response: success.data.toString()),
        );
        return KaPaymentUrlModel.fromJson(success.data);
      },
    );
  }

  Future<ChallanAutomationCronJobModel> startCronJob(
      String referenceNumber,
      ChallanSearchSource source,
      String userIdValue,
      MaharashtraSpecificCronParams? mhParams) async {
    final _restService = networkRestService;
    final result = await _restService.apiCall(
      ApiRequest(
          apiPath: ChallanAutomationConstants.getStartChallanCronJobUrl,
          requestType: RequestType.post,
          responseType: ResponseType.plain,
          requestBody: {
            "ref_num": referenceNumber,
            "source": source.name,
            "session_id": userIdValue,
            "data": mhParams?.toJson() ?? {},
          }),
    );
    return result.response.fold(
      (failure) {
        AckoLoggerManager.instance.logError(
            event: ApiErrorEvent(
                errorMessage: 'challan_automation_start_cron_job_failed',
                page: Routes.CHALLAN_WEB_PAGE,
                path: ChallanAutomationConstants
                    .getStartChallanCronJobUrl,
                type: 'POST',
                data: {"source": source.name},
                arguments: {
                  "REFERENCE_NUMBER": referenceNumber,
                  "SOURCE": source.toString(),
                  "USER_ID_VALUE": userIdValue,
                },
                errorResponse: jsonEncode({'data': failure.error.toString()})));
        return ChallanAutomationCronJobModel.error("Error Occurred");
      },
      (success) {
        AckoLoggerManager.instance.logInfo(
          event: ApiResponseInfoEvent(
              page: Routes.CHALLAN_WEB_PAGE,
              path:
                  ChallanAutomationConstants.getStartChallanCronJobUrl,
              statusCode: success.statusCode,
              data: {"source": source.name},
              arguments: {
                "REFERENCE_NUMBER": referenceNumber,
                "SOURCE": source.toString(),
                "USER_ID_VALUE": userIdValue,
              },
              response: success.data.toString()),
        );
        return ChallanAutomationCronJobModel.success("Cron Job Started");
      },
    );
  }

  Future<KaChallanHistoryModel> checkStatusOfChallan(
      {String? referenceNumber,
      String? userIdValue,
      required ChallanSearchSource source}) async {
    final _restService = networkRestService;
    final result = await _restService.apiCall(
      ApiRequest(
          apiPath: Urls.checkChallanStatus(referenceNumber ?? '', source.name, userIdValue!),
          requestType: RequestType.get),
    );
    return result.response.fold(
      (failure) {
        AckoLoggerManager.instance.logError(
            event: ApiErrorEvent(
                errorMessage: 'challan_automation_check_status_of_challan_failed',
                page: Routes.CHALLAN_WEB_PAGE,
                path: Urls.checkChallanStatus(referenceNumber ?? '', source.name, userIdValue),
                type: 'GET',
                data: {"source": source.name},
                arguments: {
                  "REFERENCE_NUMBER": referenceNumber,
                  "USER_ID_VALUE": userIdValue,
                },
                errorResponse: jsonEncode({'data' : failure.error.toString()})
            )
        );
        return KaChallanHistoryModel.error("Error Occurred");
      },
      (success) {
        AckoLoggerManager.instance.logInfo(event: ApiResponseInfoEvent(
            page: Routes.CHALLAN_WEB_PAGE,
            path: Urls.checkChallanStatus(referenceNumber ?? '', source.name, userIdValue),
            statusCode: success.statusCode,
            data: {"source": source.name},
            arguments: {
              "REFERENCE_NUMBER": referenceNumber,
              "USER_ID_VALUE": userIdValue,
            },
            response: success.data.toString()));
        return KaChallanHistoryModel.fromJson(success.data);
      },
    );
  }

  setUserIdValue(String? userIdValue, ChallanSearchSource source) async {
    final _restService = networkRestService;
    final result = await _restService.apiCall(ApiRequest(
      apiPath: ChallanAutomationConstants.getChallanUserIdValueUrl,
      requestType: RequestType.put,
      requestBody: {
        "session": userIdValue ?? '',
        "rto_source": "Karnataka",
      },
    ));
    return result.response.fold(
      (failure) {
        AckoLoggerManager.instance.logError(
            event: ApiErrorEvent(
                errorMessage: 'challan_automation_set_user_id_value_failed',
                page: Routes.CHALLAN_WEB_PAGE,
                path: ChallanAutomationConstants.getChallanUserIdValueUrl,
                type: 'PUT',
                data: {"source": source.name},
                arguments: {
                  "USER_ID_VALUE": userIdValue,
                },
                errorResponse: jsonEncode({'data' : failure.error.toString()})
            )
        );
        return ChallanAutomationUserIdModel.error("Error Occurred");
      },
      (success) {
        AckoLoggerManager.instance.logInfo(event: ApiResponseInfoEvent(
            page: Routes.CHALLAN_WEB_PAGE,
            path: ChallanAutomationConstants.getChallanUserIdValueUrl,
            statusCode: success.statusCode,
            data: {"source": source.name},
            arguments: {
              "USER_ID_VALUE": userIdValue,
            },
            response: success.data.toString()));
        return ChallanAutomationUserIdModel.fromJson(success.data);
      },
    );
  }

  Future<ChallanAutomationUserIdModel> getUserIdValue(
      String source) async {
    final _restService = networkRestService;
    final result = await _restService.apiCall(ApiRequest(
        apiPath: Urls.getChallanAutomationUserIdValue(source),
        requestType: RequestType.get));
    return result.response.fold(
      (failure) {
        AckoLoggerManager.instance.logError(
            event: ApiErrorEvent(
                errorMessage: 'challan_automation_get_user_id_value_failed',
                page: Routes.CHALLAN_WEB_PAGE,
                path: Urls.getChallanAutomationUserIdValue(source),
                type: 'GET',
                data: {"source": source},
                errorResponse: jsonEncode({'data' : failure.error.toString()})
            )
        );
        return ChallanAutomationUserIdModel.error("Error Occurred");
      },
      (success) {
        AckoLoggerManager.instance.logInfo(event: ApiResponseInfoEvent(
            page: Routes.CHALLAN_WEB_PAGE,
            path: Urls.getChallanAutomationUserIdValue(source),
            statusCode: success.statusCode,
            data: {"source": source},
            response: success.data.toString()));
        return ChallanAutomationUserIdModel.fromJson(success.data);
      },
    );
  }

  Future<UserChallanOrderHistory> getUserChallanOrderHistory(
      List<String> noticeNumbers,
      String registrationNumber,
      String source) async {
    final _restService = networkRestService;
    final result = await _restService.apiCall(ApiRequest(
        apiPath:
            Urls.getUserChallanOrderHistory(noticeNumbers[0], registrationNumber, source),
        requestType: RequestType.get));
    return result.response.fold(
      (failure) {
        AckoLoggerManager.instance.logError(
            event: ApiErrorEvent(
                errorMessage: 'challan_automation_get_user_challan_order_history_failed',
                page: Routes.CHALLAN_WEB_PAGE,
                path: Urls.getUserChallanOrderHistory(noticeNumbers[0], registrationNumber, source),
                type: 'GET',
                data: {"source": source},
                arguments: {
                  "NOTICE_NUMBER": noticeNumbers,
                  "REGISTRATION_NUMBER": registrationNumber
                },
                errorResponse: jsonEncode({'data' : failure.error.toString()})
            )
        );
        return UserChallanOrderHistory.error("Error Occurred");
      },
      (success) {
        AckoLoggerManager.instance.logInfo(event: ApiResponseInfoEvent(
            page: Routes.CHALLAN_WEB_PAGE,
            path: Urls.getUserChallanOrderHistory(noticeNumbers[0], registrationNumber, source),
            statusCode: success.statusCode,
            data: {"source": source},
            arguments: {
              "NOTICE_NUMBER": noticeNumbers,
              "REGISTRATION_NUMBER": registrationNumber
            },
            response: success.data.toString()));
        return UserChallanOrderHistory.fromJson(success.data);
      },
    );
  }

  Future<ChallanAutomationPaymentStatus> getUserPaymentStatus(List<String> noticeNumbers,
      String registrationNumber, String source) async {
    final _restService = networkRestService;
    final result = await _restService.apiCall(ApiRequest(
        apiPath: Urls.getUserChallanStatus(noticeNumbers[0], registrationNumber, source),
        requestType: RequestType.get));
    return result.response.fold(
      (failure) {
        AckoLoggerManager.instance.logError(
            event: ApiErrorEvent(
                errorMessage: 'challan_automation_get_user_payment_status_failed',
                page: Routes.CHALLAN_WEB_PAGE,
                path: Urls.getUserChallanOrderHistory(noticeNumbers[0], registrationNumber, source),
                type: 'GET',
                data: {"source": source},
                arguments: {
                  "NOTICE_NUMBER": noticeNumbers,
                  "REGISTRATION_NUMBER": registrationNumber
                },
                errorResponse: jsonEncode({'data' : failure.error.toString()})
            )
        );
        return ChallanAutomationPaymentStatus.error("Error Occurred");
      },
      (success) {
        AckoLoggerManager.instance.logInfo(event: ApiResponseInfoEvent(
            page: Routes.CHALLAN_WEB_PAGE,
            path: Urls.getUserChallanOrderHistory(noticeNumbers[0], registrationNumber, source),
            statusCode: success.statusCode,
            data: {"source": source},
            arguments: {
              "NOTICE_NUMBER": noticeNumbers,
              "REGISTRATION_NUMBER": registrationNumber
            },
            response: success.data.toString()));
        return ChallanAutomationPaymentStatus.fromJson(success.data);
      },
    );
  }

  Future<DocumentServiceResponse> uploadChallanReceipt(
      String documentName,
      String documentPath,
      String source,
      ) async {
    Map<String, dynamic> formData = {
      "documentGroupCreateRequest": MultipartFile.fromString(
          jsonEncode({
            "lob": "central",
            "reference_type": "user",
            "reference_id": "",
            "attribute": "transactional",
            "journey": "challan_payment",
            "document_list": [
              {
                "document_type": "challan_payment_receipt",
                "file_name": documentName,
                "metadata": {
                  "expiry_timestamp": "",
                  "file_extension": documentPath.split(".").last
                }
              }
            ]
          }),
          contentType: MediaType.parse('application/json')),
      "documents":
      await MultipartFile.fromFile(documentPath, filename: documentName)
    };

    final _restService = networkRestService;
    final result = await _restService.apiCall(ApiRequest(
        apiPath: ApiPath.ASSETS_UPLOAD_DOC_URL.replaceAll('v2', 'v1'),
        requestType: RequestType.postFormData,
        headers: {'content-type': 'multipart/form-data'},
        formData: formData,
    ));

    return result.response.fold(
            (failure) {
              AckoLoggerManager.instance.logError(
                  event: ApiErrorEvent(
                      errorMessage: 'challan_automation_upload_challan_receipt_failed',
                      page: Routes.CHALLAN_WEB_PAGE,
                      path: ApiPath.ASSETS_UPLOAD_DOC_URL.replaceAll('v2', 'v1'),
                      type: 'postFormData',
                      data: {"source": source},
                      arguments: {
                        "DOCUMENT_NAME": documentName,
                        "DOCUMENT_PATH": documentPath
                      },
                      errorResponse: jsonEncode({'data' : failure.error.toString()})
                  )
              );
              return DocumentServiceResponse.error("Document Upload Failed");
            },
            (success) {
              AckoLoggerManager.instance.logInfo(event: ApiResponseInfoEvent(
                  page: Routes.CHALLAN_WEB_PAGE,
                  path: ApiPath.ASSETS_UPLOAD_DOC_URL.replaceAll('v2', 'v1'),
                  statusCode: success.statusCode,
                  data: {"source": source},
                  arguments: {
                    "DOCUMENT_NAME": documentName,
                    "DOCUMENT_PATH": documentPath
                  },
                  response: success.data.toString()));
              return DocumentServiceResponse.fromJson(success.data);
            });
  }

  Future<UpiRequestResponseModel> sendUpiRequestToUser(String merchantId, String bdOrderId, String authToken,
      String upiId, String source) async {
    final _restService = networkRestService;
    final result = await _restService.apiCall(ApiRequest(
      apiPath: ChallanAutomationConstants.sendUpiRequestUrl,
      requestType: RequestType.post,
      headers: {
        "Authorization": authToken,
      },
      requestBody: {
        "merchant_id": merchantId,
        "order_id": bdOrderId,
        "upi_id": upiId
      },
    ));
    return result.response.fold((failure) {
      AckoLoggerManager.instance.logError(
          event: ApiErrorEvent(
              errorMessage:
                  'challan_automation_send_upi_request_to_user_failed',
              page: Routes.CHALLAN_WEB_PAGE,
              path: ChallanAutomationConstants.sendUpiRequestUrl,
              type: 'post',
              data: {"source": source},
              arguments: {
                "MERCHANT_ID": merchantId,
                "BD_ORDER_ID": bdOrderId,
                "SOURCE": source,
                "AUTH_TOKEN": authToken,
              },
              errorResponse: jsonEncode({'data': failure.error.toString()})));
      return UpiRequestResponseModel.error("Upi Request Failed");
    }, (success) {
      AckoLoggerManager.instance.logInfo(
          event: ApiResponseInfoEvent(
              page: Routes.CHALLAN_WEB_PAGE,
              path: ChallanAutomationConstants.sendUpiRequestUrl,
              statusCode: success.statusCode,
              data: {"source": source},
              arguments: {
                "MERCHANT_ID": merchantId,
                "BD_ORDER_ID": bdOrderId,
                "SOURCE": source,
                "AUTH_TOKEN": authToken,
              },
              response: success.data.toString()));
      return UpiRequestResponseModel.fromJson(success.data);
    });
  }

  Future<UpiRequestPollingResponseModel> checkMhPaymentStatus(
    String merchantId,
    String bdOrderId,
    String transactionId,
    String searchSource,
    String authToken,
  ) async {
    final _restService = networkRestService;
    final result = await _restService.apiCall(ApiRequest(
        apiPath: ChallanAutomationConstants.checkUpiRequestStatusUrl,
        requestType: RequestType.post,
        headers: {
          "Authorization": authToken,
        },
        requestBody: {
          "merchant_id": merchantId,
          "order_id": bdOrderId,
          "transaction_id": transactionId
        }));
    return result.response.fold(
      (failure) {
        AckoLoggerManager.instance.logError(
            event: ApiErrorEvent(
                errorMessage:
                    'challan_automation_check_ts_payment_status_failed',
                page: Routes.CHALLAN_WEB_PAGE,
                path:
                    ChallanAutomationConstants.checkUpiRequestStatusUrl,
                type: 'POST',
                data: {"source": searchSource},
                arguments: {
                  "MERCHANT_ID": merchantId,
                  "BD_ORDER_ID": bdOrderId,
                  "TRANSACTION_ID": transactionId,
                  "AUTH_TOKEN": authToken,
                },
                errorResponse: jsonEncode({'data': failure.error.toString()})));
        return UpiRequestPollingResponseModel.error(
            "Payment Status Check Failed");
      },
      (success) {
        AckoLoggerManager.instance.logInfo(
            event: ApiResponseInfoEvent(
                page: Routes.CHALLAN_WEB_PAGE,
                path:
                    ChallanAutomationConstants.checkUpiRequestStatusUrl,
                statusCode: success.statusCode,
                data: {"source": searchSource},
                arguments: {
                  "MERCHANT_ID": merchantId,
                  "BD_ORDER_ID": bdOrderId,
                  "TRANSACTION_ID": transactionId,
                  "AUTH_TOKEN": authToken,
                },
                response: success.data.toString()));
        return UpiRequestPollingResponseModel.fromJson(success.data);
      },
    );
  }

  sendPaymentStatus(
      {required List<String> noticeNumbers,
      required String referenceNumber,
      required String registrationNumber,
      required ChallanSearchSource source,
      required ChallanPaymentStatus ackoPaymentStatus,
      required String phoneNumber,
      required bool shouldSendHistroyProps,
      String? challanAmount,
      String? paymentStatus,
      String? receiptUrl}) async {
    final _restService = networkRestService;
    final result = await _restService.apiCall(ApiRequest(
      apiPath: ChallanAutomationConstants.sendUserChallanStatusUrl,
      requestType: RequestType.put,
      requestBody: {
        "challan_number": noticeNumbers,
        "ref_number": referenceNumber,
        "registration_number": registrationNumber,
        "rto_source": source.name,
        "payment_status": ackoPaymentStatus.name,
        if (shouldSendHistroyProps)
          "challan_payment_order_history": {
            "notice_number": noticeNumbers[0],
            "registration_number": registrationNumber,
            "paid_by": phoneNumber,
            "ref_number": referenceNumber,
            "challan_amount": challanAmount,
            "payment_status": ackoPaymentStatus.name,
            "receipt_file_url": receiptUrl,
            "payment_mode": "UPI"
          }
      },
    ));
    return result.response.fold(
      (failure) {
        AckoLoggerManager.instance.logError(
            event: ApiErrorEvent(
                errorMessage: 'challan_automation_send_payment_status_failed',
                page: Routes.CHALLAN_WEB_PAGE,
                path: ChallanAutomationConstants.sendUserChallanStatusUrl,
                type: 'PUT',
                data: {"source": source.name},
                arguments: {
                  "NOTICE_NUMBER": noticeNumbers,
                  "REFERENCE_NUMBER": referenceNumber,
                  "REGISTRATION_NUMBER": registrationNumber,
                  "ACKO_PAYMENT_STATUS": ackoPaymentStatus.toString(),
                  "SHOULD_SEND_HISTORY": shouldSendHistroyProps,
                  "RECEIPT_FILE_URL": receiptUrl,
                  "CHALLAN_AMOUNT": challanAmount,
                },
                errorResponse: jsonEncode({'data' : failure.error.toString()})
            )
        );
        return KaPaymentStatusIngestionModel.error("Error Occurred");
      },
      (success) {
        AckoLoggerManager.instance.logInfo(
            event: ApiResponseInfoEvent(
                page: Routes.CHALLAN_WEB_PAGE,
                path: ChallanAutomationConstants.sendUserChallanStatusUrl,
                statusCode: success.statusCode,
                data: {"source": source.name},
                arguments: {
                  "NOTICE_NUMBER": noticeNumbers,
                  "REFERENCE_NUMBER": referenceNumber,
                  "REGISTRATION_NUMBER": registrationNumber,
                  "ACKO_PAYMENT_STATUS": ackoPaymentStatus.toString(),
                  "SHOULD_SEND_HISTORY": shouldSendHistroyProps,
                  "RECEIPT_FILE_URL": receiptUrl,
                  "CHALLAN_AMOUNT": challanAmount,
                },
                response: success.data.toString()));
        return KaPaymentStatusIngestionModel.success("Success");
      },
    );
  }
}
