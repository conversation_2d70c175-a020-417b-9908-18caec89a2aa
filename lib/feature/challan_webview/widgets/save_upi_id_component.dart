import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:design_module/design_module.dart';
import 'package:flutter/material.dart';

import '../../../common/util/color_constants.dart';
import '../../../common/view/acko_text_config.dart';

class SaveUpiIdComponent extends StatefulWidget {
  final Function(bool) onCheckboxTicked;

  const SaveUpiIdComponent({super.key, required this.onCheckboxTicked});


  @override
  State<SaveUpiIdComponent> createState() => _SaveUpiIdComponentState();
}

class _SaveUpiIdComponentState extends State<SaveUpiIdComponent> {
  bool _saveUserUpiId = false;


  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Theme(
          data: Theme.of(context).copyWith(
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            visualDensity: VisualDensity.compact,
          ),
          child: Checkbox(
            value: _saveUserUpiId,
            activeColor: color0FA457,
            onChanged: (bool? newValue) {
              setState(() {
                _saveUserUpiId = newValue ?? false;
                widget.onCheckboxTicked(_saveUserUpiId);
              });
            },
          ),
        ),
        SizedBox(width: 4,),
        AckoTextConfig.i.paragraphSmall.text(ChallanAutomationConstants.saveMyUpiId),
      ],
    );
  }
}
