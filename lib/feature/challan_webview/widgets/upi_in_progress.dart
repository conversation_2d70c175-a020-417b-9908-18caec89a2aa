import 'dart:async';
import 'dart:math';

import 'package:acko_flutter/common/view/acko_text_config.dart';
import 'package:acko_flutter/feature/challan_webview/models/challan_automation_misc_models.dart';
import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../common/util/color_constants.dart';
import '../../challan/challan_utility.dart';
import '../challan_web_view_bloc.dart';
import 'cancel_automation_bottomsheet.dart';

class UpiInProgressWidget extends StatefulWidget {
  final String? challanAmount;
  final int timerInSeconds;

  const UpiInProgressWidget({
    Key? key,
    this.challanAmount,
    required this.timerInSeconds,
  }) : super(key: key);

  @override
  State<UpiInProgressWidget> createState() => _UpiInProgressWidgetState();
}

class _UpiInProgressWidgetState extends State<UpiInProgressWidget> {
  ChallanWebViewBloc? _bloc;
  late ChallanPaymentInProgress paymentInProgressConstants;

  @override
  void initState() {
    super.initState();
    _bloc = BlocProvider.of(context);
    paymentInProgressConstants = ChallanUtility.getPaymentInProgressConstants(
        _bloc?.userFlowData.userUpiId ?? '');
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CircularTimer(upiImageUrl: paymentInProgressConstants.imageUrl, totalTimeInSeconds: widget.timerInSeconds,),
        if (widget.challanAmount != null)
          SizedBox(
            height: 10,
          ),
        (widget.challanAmount != null)
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AckoTextConfig.i.paragraphMedium
                      .text(ChallanAutomationConstants.numberedChallanMessage(_bloc?.violationNumbers.length ?? 1)),
                  AckoTextConfig.i.paragraphMedium.text(
                      ChallanAutomationConstants.rupeeSymbol + widget.challanAmount!,
                      textColor: color4B4B4B),
                ],
              )
            : SizedBox.shrink(),
        if (widget.challanAmount != null) SizedBox(height: 12),
        AckoTextConfig.i.headingSmall.text(paymentInProgressConstants.title,
            textAlign: TextAlign.center),
        Spacer(),
        GestureDetector(
          onTap: () {
            context.showAckoModalBottomSheet(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: CancelAutomationBottomsheet(),
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: AckoTextConfig.i.labelMedium
                .text(ChallanAutomationConstants.cancelPayment, textColor: color1B73E8),
          ),
        ),
      ],
    );
  }
}

class CircularTimer extends StatefulWidget {
  final String upiImageUrl;
  final int totalTimeInSeconds;
  const CircularTimer({Key? key, required this.upiImageUrl, required this.totalTimeInSeconds}) : super(key: key);

  @override
  State<CircularTimer> createState() => _CircularTimerState();
}

class _CircularTimerState extends State<CircularTimer>
    with SingleTickerProviderStateMixin {
  late int _totalTimeInSeconds;
  late int _remainingSeconds;
  Timer? _timer;

  late AnimationController _flipController;

  @override
  void initState() {
    super.initState();
    _totalTimeInSeconds = widget.totalTimeInSeconds;
    _remainingSeconds = _totalTimeInSeconds;
    _startTimer();

    _flipController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 700),
    );

    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        _flipController.forward();
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _flipController.dispose();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _remainingSeconds--;
        if (_remainingSeconds <= 0) {
          _remainingSeconds = 0;
          timer.cancel();
        }
      });
    });
  }

  String _formatTime(int totalSeconds) {
    final minutes = totalSeconds ~/ 60;
    final seconds = totalSeconds % 60;
    final minutesStr = minutes.toString().padLeft(2, '0');
    final secondsStr = seconds.toString().padLeft(2, '0');
    return '$minutesStr:$secondsStr';
  }

  @override
  Widget build(BuildContext context) {
    final progressValue = _remainingSeconds / _totalTimeInSeconds;

    return Center(
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            height: 150,
            width: 150,
            child: CircularProgressIndicator(
              value: progressValue,
              strokeWidth: 8,
              color: color541AC3,
              backgroundColor: colorF5F5F5,
            ),
          ),
          AnimatedBuilder(
            animation: _flipController,
            builder: (context, child) {
              final angle = _flipController.value * pi;
              final showFront = angle < pi / 2;

              return Transform(
                alignment: Alignment.center,
                transform: Matrix4.rotationY(angle),
                child: showFront
                    ? _buildFrontSide()
                    : Transform(
                        alignment: Alignment.center,
                        transform: Matrix4.rotationY(pi),
                        child: _buildBackSide(),
                      ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFrontSide() {
    return SizedBox(
      height: 80,
      width: 80,
      child: ClipOval(
        child: HybridImage(
          imageUrl: widget.upiImageUrl,
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  Widget _buildBackSide() {
    final timeText = _formatTime(_remainingSeconds);
    return SizedBox(
      height: 120,
      width: 120,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AckoTextConfig.i.headingMedium.text(
            timeText,
          ),
          const SizedBox(height: 6),
          AckoTextConfig.i.paragraphXXSmall.text(
            ChallanAutomationConstants.minsRemaining,
          ),
        ],
      ),
    );
  }
}
