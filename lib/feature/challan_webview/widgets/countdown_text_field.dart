import 'dart:async';
import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:design_module/uikit/widgets/button/uikit_button.dart';
import 'package:flutter/material.dart';

import '../../../common/view/acko_text_config.dart';
import '../enums/challan_webview_enums.dart';

class OtpTextField extends StatefulWidget {
  final int countdownSeconds;
  final ValueChanged<String> onSubmit;
  final Function(HelperScript) helperScript;
  final int textfieldLength;
  final String title;
  final String subtitle;
  final String? errorMessage;

  const OtpTextField({
    Key? key,
    required this.countdownSeconds,
    required this.onSubmit,
    required this.helperScript,
    required this.textfieldLength,
    required this.title,
    required this.subtitle,
    this.errorMessage,
  }) : super(key: key);

  @override
  _OtpTextFieldState createState() => _OtpTextFieldState();
}

class _OtpTextFieldState extends State<OtpTextField> {
  late int _remainingTime;
  Timer? _timer;
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  void _startTimer() {
    setState(() {
      _remainingTime = widget.countdownSeconds;
    });
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingTime > 0) {
        setState(() {
          _remainingTime--;
        });
      } else {
        _timer?.cancel();
      }
    });
  }

  void _resetTimer() {
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _controller.dispose();
    super.dispose();
  }

  double _getPinBoxSize() {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return (MediaQuery.of(context).size.width - 110) / 4;
    } else {
      return 60.0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AckoTextConfig.i.headingSmall.text(widget.title),
        SizedBox(
          height: 4,
        ),
        AckoTextConfig.i.paragraphSmall.text(widget.subtitle),
        SizedBox(
          height: 28,
        ),
        TextField(
          controller: _controller,
          autofocus: true,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        const SizedBox(height: 10),
        AckoTextConfig.i.paragraphSmall
            .text('Time remaining: $_remainingTime s', textColor: colorFD6055),
        (widget.errorMessage != null)
            ? Column(
                children: [
                  const SizedBox(height: 12),
                  AckoTextConfig.i.paragraphSmall
                      .text(widget.errorMessage!, textColor: colorF75276)
                ],
              )
            : SizedBox.shrink(),
        const SizedBox(height: 12),
        GestureDetector(
          onTap: _remainingTime == 0
              ? () {
                  widget.helperScript(HelperScript.resend_otp);
                  _resetTimer();
                }
              : null,
          child: Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: AckoTextConfig.i.labelMedium.text(ChallanAutomationConstants.resendOtp,
                textColor: (_remainingTime == 0) ? color1B73E8 : Colors.grey),
          ),
        ),
        Spacer(),
        Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: AckoDarkButtonFullWidth(
              text: ChallanAutomationConstants.submit,
              buttonState: (_controller.text.length == 0)
                  ? UIKitButtonState.disabled
                  : UIKitButtonState.active,
              onTap: () {
                widget.onSubmit(_controller.text);
                _controller.clear();
              }),
        ),
      ],
    );
  }
}
