import 'package:acko_flutter/common/view/acko_text_config.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/material.dart';
import 'package:utilities/constants/constants.dart';

import '../../../util/Utility.dart';

class ChallanWebviewBottomsheet extends StatefulWidget {
  final String title;
  final String subtitle;
  final String buttonCta;
  final String? imageUrl;
  const ChallanWebviewBottomsheet(
      {super.key,
      required this.title,
      required this.subtitle,
      required this.buttonCta,
      this.imageUrl});

  @override
  State<ChallanWebviewBottomsheet> createState() =>
      _ChallanWebviewBottomsheetState();
}

class _ChallanWebviewBottomsheetState extends State<ChallanWebviewBottomsheet> {
  @override
  void initState() {
    super.initState();
    AnalyticsTrackerManager.instance.sendEvent(
        event: PageLoadedConstants.APP_VIEW_PAGE,
        properties: {
          'platform': Util.getPlatform(),
          'page_name': widget.title
        });
  }

  _onButtonTap() {
    Navigator.of(context)
      ..pop()
      ..pop();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          HybridImage(
            imageUrl: widget.imageUrl ?? AssetsConstants.pay_money,
            height: 80,
            width: 80,
          ),
          SizedBox(
            height: 20,
          ),
          AckoTextConfig.i.headingSmall
              .text(widget.title, textAlign: TextAlign.center),
          SizedBox(
            height: 10,
          ),
          AckoTextConfig.i.paragraphSmall
              .text(widget.subtitle, textAlign: TextAlign.center),
          SizedBox(
            height: 20,
          ),
          AckoDarkButtonFullWidth(
              text: widget.buttonCta,
              onTap: () {
                _onButtonTap();
              })
        ],
      ),
    );
  }
}
