import 'package:acko_flutter/common/view/acko_text_config.dart';
import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/material.dart';
import 'package:utilities/constants/constants.dart';

class CancelAutomationBottomsheet extends StatelessWidget {
  const CancelAutomationBottomsheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        HybridImage(
          imageUrl: AssetsConstants.unable_to_process,
          height: 80,
          width: 80,
        ),
        SizedBox(
          height: 8,
        ),
        AckoTextConfig.i.headingSmall
            .text(ChallanAutomationConstants.areYouSureYouWantToCancel),
        Sized<PERSON>ox(height: 8),
        AckoTextConfig.i.paragraphSmall.text(
            ChallanAutomationConstants.pendingChallanConsequences,
            textAlign: TextAlign.center),
        SizedBox(height: 24),
        Row(
          children: [
            Expanded(
                child: AckoDarkButtonFullWidth(
                    text: ChallanAutomationConstants.no,
                    onTap: () {
                      Navigator.pop(context);
                    })),
            SizedBox(width: 8),
            Expanded(
                child: AckoDarkButtonFullWidth(
                    text: ChallanAutomationConstants.yesCancel,
                    onTap: () {
                      Navigator.of(context)
                        ..pop()
                        ..pop();
                    }))
          ],
        ),
      ],
    );
  }
}
