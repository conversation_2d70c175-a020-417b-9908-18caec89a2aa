import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:flutter/material.dart';

import '../../../common/view/acko_text_config.dart';

class ShowInfoWidget extends StatelessWidget {
  final String title;
  final String message;
  final Function(String)? onSubmit;
  const ShowInfoWidget(
      {super.key,
      required this.title,
      required this.message,
      required this.onSubmit});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  AckoTextConfig.i.headingSmall
                      .text(title, textAlign: TextAlign.center),
                  <PERSON><PERSON><PERSON><PERSON>(height: 8),
                  AckoTextConfig.i.paragraphSmall
                      .text(message, textAlign: TextAlign.center),
                ],
              ),
            ),
          ),
          AckoDarkButtonFullWidth(
            text: ChallanAutomationConstants.okay,
            onTap: () {
              onSubmit!("true");
            },
          ),
        ],
      ),
    );
  }
}
