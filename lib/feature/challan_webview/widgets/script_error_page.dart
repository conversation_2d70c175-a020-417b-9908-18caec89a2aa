import 'package:acko_flutter/common/view/acko_text_config.dart';
import 'package:acko_flutter/feature/challan_webview/utils/challan_webview_utility.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/material.dart';

import '../models/challan_automation_misc_models.dart';

class ScriptErrorPage extends StatefulWidget {
  final bool shouldTryAgain;
  final VoidCallback? onErrorTapped;
  final Function(String)? onSubmit;
  ScriptErrorPage(
      {super.key,
        required this.shouldTryAgain,
        this.onErrorTapped,
        this.onSubmit,}
      );

  @override
  State<ScriptErrorPage> createState() => _ScriptErrorPageState();
}

class _ScriptErrorPageState extends State<ScriptErrorPage> {
  late ErrorScreenConstants errorScreenConstants;

  @override
  void initState() {
    super.initState();
    errorScreenConstants = ChallanWebviewUtility.getErrorScreenConstants(widget.shouldTryAgain);
  }

  onButtonTapped() {
    if (widget.shouldTryAgain && widget.onSubmit != null && widget.onErrorTapped != null) {
        widget.onSubmit!("true");
        widget.onErrorTapped!();
    } else {
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Stack(
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              HybridImage(
                  imageUrl: errorScreenConstants.mainImage,
                height: 80,
                width: 80,
              ),
              SizedBox(height: 16,),
              AckoTextConfig.i.headingSmall.text(errorScreenConstants.title),
              SizedBox(height: 8,),
              AckoTextConfig.i.paragraphSmall.text(errorScreenConstants.subtitle, textAlign: TextAlign.center),
            ],
        ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: AckoDarkButtonFullWidth(
                text: errorScreenConstants.buttonText,
                onTap: () {
                  onButtonTapped();
                }),
          )
        ],
      ),
    );
  }
}
