import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/acko_text_config.dart';
import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:design_module/design_module.dart';
import 'package:flutter/material.dart';

import '../models/challan_keyword_model.dart';

class ChallanAmountBreakupWidget extends StatefulWidget {
  final AmountBreakupProperties breakDownProps;
  const ChallanAmountBreakupWidget({super.key, required this.breakDownProps});

  @override
  State<ChallanAmountBreakupWidget> createState() => _ChallanAmountBreakupWidgetState();
}

class _ChallanAmountBreakupWidgetState extends State<ChallanAmountBreakupWidget> {
  @override
  Widget build(BuildContext context) {
    if (widget.breakDownProps.showBreakupSection == false) {
      return const SizedBox.shrink();
    }
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: colorE8E8E8,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(14),
            child: ExpansionTile(
              tilePadding: EdgeInsets.zero,
              shape: const Border(
                top: BorderSide.none,
                bottom: BorderSide.none,
              ),
              collapsedShape: const Border(
                top: BorderSide.none,
                bottom: BorderSide.none,
              ),
              backgroundColor: Colors.transparent,
              collapsedBackgroundColor: Colors.transparent,
              title: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AckoTextConfig.i.labelMedium.text('Challan Amount'),
                    AckoTextConfig.i.headingXXSmall.text(ChallanAutomationConstants.rupeeSymbol + widget.breakDownProps.totalPayable!),
                  ],
                ),
                children: [
                  for (final entry in widget.breakDownProps.breakup!.entries)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          AckoTextConfig.i.paragraphSmall.text(entry.key),
                          Builder(builder: (_) {
                            final rawValue = entry.value.toString();
                            final num? parsed = num.tryParse(rawValue);
                            if (parsed != null) {
                              return AckoTextConfig.i.headingXXSmall.text(ChallanAutomationConstants.rupeeSymbol +  entry.value.toString());
                            } else {
                              return AckoTextConfig.i.headingXXSmall.text(entry.value.toString());
                            }
                          }),
                        ],
                      ),
                    ),
                  SizedBox(height: 6,),
                  Divider(thickness: 1,),
                  SizedBox(height: 6,),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      AckoTextConfig.i.paragraphSmall.text('Total payable'),
                      AckoTextConfig.i.headingXXSmall.text(ChallanAutomationConstants.rupeeSymbol + widget.breakDownProps.totalPayable!),
                    ],
                  )
                ],
            ),
          ),
        ),
        SizedBox(height: 12,),
      ],
    );
  }
}
