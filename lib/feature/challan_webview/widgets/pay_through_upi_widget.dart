import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/acko_text_config.dart';
import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:acko_flutter/feature/challan_webview/widgets/save_upi_id_component.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:design_module/uikit/widgets/button/uikit_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../challan/challan_enums.dart';
import '../../challan/challan_utility.dart';
import '../../challan/view/add_new_upi_id_bottomsheet.dart';
import '../challan_web_view_bloc.dart';
import '../models/challan_keyword_model.dart';
import 'challan_amount_breakup_widget.dart';
import 'challan_upi_tile.dart';

class PayThroughUpiWidget extends StatefulWidget {
  final Function(String) onSubmit;
  final String title;
  final String? subtitle;
  final String? amount;
  final List<String>? userUpiIds;
  final ErrorProps? errorProps;

  const PayThroughUpiWidget(
      {Key? key,
      required this.onSubmit,
      required this.title,
      this.subtitle,
      this.amount,
      this.errorProps,
      this.userUpiIds})
      : super(key: key);

  @override
  _PayThroughUpiWidgetState createState() => _PayThroughUpiWidgetState();
}

class _PayThroughUpiWidgetState extends State<PayThroughUpiWidget> {
  final TextEditingController _controller = TextEditingController();
  bool _saveUserUpiId = false;
  bool _isFullWidthButtonLoading = false;
  ChallanWebViewBloc? _bloc;
  String? selectedUpiID;
  String? customUpiID;

  @override
  void initState() {
    _bloc = BlocProvider.of(context);
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }

  bool get _hasText => _controller.text.trim().isNotEmpty;

  @override
  void didUpdateWidget(covariant PayThroughUpiWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    selectedUpiID = null;
    customUpiID = null;
    if (_isFullWidthButtonLoading) {
      setState(() {
        _isFullWidthButtonLoading = false;
      });
    }
  }

  void _handleSubmit(String upiID) {
    setState(() {
      selectedUpiID = upiID;
    });
    widget.onSubmit(upiID);
  }

  @override
  Widget build(BuildContext context) {
    final hasUpi = widget.userUpiIds?.isNotEmpty == true;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AckoTextConfig.i.headingSmall.text(
          hasUpi
              ? ChallanAutomationConstants.payUsingUpiID
              : ChallanAutomationConstants.enterUpiId,
        ),
        const SizedBox(height: 12),
        if (_bloc?.userFlowData.amountBreakup != null) ...[
          ChallanAmountBreakupWidget(
            breakDownProps: _bloc!.userFlowData.amountBreakup!,
          ),
          const SizedBox(height: 12),
        ],

        if (hasUpi)
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  if (widget.errorProps?.errorMessage != null) ...[
                    ChallanUpiErrorWidget(
                      errorMessage: widget.errorProps!.errorMessage,
                    ),  
                    const SizedBox(height: 12),
                  ],
                  ...widget.userUpiIds!.map((upiId) {
                    final tileConstants = ChallanUtility.getUpiConstantsFromUpiId(upiId);
                    return UpiTile(
                      isTileDisabled: selectedUpiID != null &&
                          selectedUpiID != upiId &&
                          customUpiID == null,
                      isTileLoading: (selectedUpiID != null &&
                          selectedUpiID == upiId) &&
                          customUpiID == null,
                      upiTileConstants: tileConstants,
                      onTap: () => _handleSubmit(upiId),
                    );
                  }).toList(),
                  const SizedBox(height: 12),
                  CustomUpiIdWidget(
                    isTileLoading: (selectedUpiID != null && customUpiID != null)
                        && (customUpiID == selectedUpiID),
                    isTileDisabled:
                    (selectedUpiID != null && customUpiID == null),
                    onTap: () {
                      context.showAckoModalBottomSheet(
                        child: BlocProvider.value(
                          value: context.read<ChallanWebViewBloc>(),
                          child: AddNewUpiIdBottomsheet(
                            onSubmit: (String value) {
                              customUpiID = value;
                              _handleSubmit(value);
                            },
                          ),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          )
        else
          ...[
            const SizedBox(height: 4),
            TextField(
              controller: _controller,
              autofocus: true,
              onChanged: (_) => setState(() {}),
              decoration: InputDecoration(
                hintText: ChallanAutomationConstants.enterUpiId,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 24),
            SaveUpiIdComponent(
              onCheckboxTicked: (checked) {
                _bloc?.userFlowData.saveUserUpiID = checked;
              },
            ),
            const SizedBox(height: 12),
            if (widget.errorProps?.errorMessage != null)
              AckoTextConfig.i.paragraphSmall.text(
                widget.errorProps!.errorMessage,
                textColor: colorEE1520,
              ),
            const Spacer(),
            AckoDarkButtonFullWidth(
              text: ChallanAutomationConstants.payNow,
              onTap: () {
                if (!_hasText && !hasUpi) return;
                setState(() => _isFullWidthButtonLoading = true);
                _bloc?.userFlowData.saveUserUpiID = _saveUserUpiId;
                _handleSubmit(hasUpi ? selectedUpiID! : _controller.text);
              },
              buttonState: _isFullWidthButtonLoading
                  ? UIKitButtonState.loading
                  : ((_hasText || hasUpi)
                  ? UIKitButtonState.active
                  : UIKitButtonState.disabled),
            ),
          ],
      ],
    );
  }
}

class UpiTileConstants {
  final String? title;
  final String subtitle;
  final ChallanUpiProvider upiProvider;
  final String imageUrl;

  UpiTileConstants(
      {this.title,
      required this.subtitle,
      required this.upiProvider,
      required this.imageUrl});
}
