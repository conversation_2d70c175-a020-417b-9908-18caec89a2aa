import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/acko_text_config.dart';
import 'package:acko_flutter/feature/challan_webview/models/challan_keyword_model.dart';
import 'package:acko_flutter/feature/challan_webview/utils/challan_webview_utility.dart';
import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/uikit/widgets/loaders/uikit_loading_dots.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/material.dart';

import '../../../util/Utility.dart';
import '../enums/challan_webview_enums.dart';

class TrackingLoadingScreen extends StatefulWidget {
  final TrackingLoadingScreenProperties screenProps;
  const TrackingLoadingScreen({super.key, required this.screenProps});

  @override
  State<TrackingLoadingScreen> createState() => _TrackingLoadingScreenState();
}

class _TrackingLoadingScreenState extends State<TrackingLoadingScreen> {

  @override
  void initState() {
    super.initState();
    AnalyticsTrackerManager.instance
        .sendEvent(event: PageLoadedConstants.APP_VIEW_PAGE, properties: {
      'platform': Util.getPlatform(),
      'page_name': 'connecting to RTO loader'
    });
  }


  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          HybridImage(
            imageUrl:
            ChallanAutomationConstants.imageTrackingScreenEvaluation,
            height: 80,
            width: 80,
          ),
          SizedBox(
            height: 8,
          ),
          AckoTextConfig.i.headingSmall.text(widget.screenProps.title),
          SizedBox(
            height: 24,
          ),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: MediaQuery.of(context).size.width * 0.1,
            ),
            child: Container(
              decoration: BoxDecoration(
                color: colorF5F5F5,
                borderRadius: BorderRadius.circular(16.0),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: widget.screenProps.subtitles
                      .asMap()
                      .entries
                      .map((entry) => [
                        SubtitleRow(
                            title: entry.value.text, loadingState: entry.value.loadingState
                        ),
                        if (entry.key != widget.screenProps.subtitles.length - 1)
                          const SizedBox(height: 20),
                    ])
                  .expand((widgetList) => widgetList)
                  .toList(),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class SubtitleRow extends StatefulWidget {
  final String title;
  final TrackingScreenStateType loadingState;

  const SubtitleRow({
    Key? key,
    required this.title,
    required this.loadingState,
  }) : super(key: key);

  @override
  State<SubtitleRow> createState() => _SubtitleRowState();
}

class _SubtitleRowState extends State<SubtitleRow>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );

    if (widget.loadingState == TrackingScreenStateType.loaded) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(covariant SubtitleRow oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.loadingState != TrackingScreenStateType.loaded &&
        widget.loadingState == TrackingScreenStateType.loaded) {
      _controller.forward(from: 0.0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final constants = ChallanWebviewUtility.getChallanTrackingScreenConstants(widget.loadingState);

    Widget imageWidget = HybridImage(imageUrl: constants.stateImage);

    if (widget.loadingState == TrackingScreenStateType.loading) {
      imageWidget = Stack(
        alignment: Alignment.center,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.black,
                width: 2.0,
              ),
            ),
          ),
          const UiKitLoadingDots(dotSize: 2.0),
        ],
      );
    } else if (widget.loadingState == TrackingScreenStateType.loaded) {
      imageWidget = ScaleTransition(
        scale: _scaleAnimation,
        child: HybridImage(imageUrl: constants.stateImage),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        imageWidget,
        const SizedBox(width: 12),
        Expanded(
          child: widget.loadingState == TrackingScreenStateType.loading
              ? AckoTextConfig.i.labelSmall.text(widget.title, maxLines: 2, softWrap: true)
              : AckoTextConfig.i.paragraphSmall.text(widget.title, maxLines: 2, softWrap: true),
        ),
      ],
    );
  }
}
