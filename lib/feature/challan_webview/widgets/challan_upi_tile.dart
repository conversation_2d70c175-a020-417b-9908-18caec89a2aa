import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:acko_flutter/feature/challan_webview/widgets/pay_through_upi_widget.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/uikit/widgets/loaders/uikit_loading_dots.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/material.dart';
import 'package:utilities/constants/constants.dart';

import '../../../common/util/color_constants.dart';
import '../../../common/view/acko_text_config.dart';
import '../models/challan_keyword_model.dart';

class UpiTile extends StatefulWidget {
  final UpiTileConstants upiTileConstants;
  final VoidCallback onTap;
  final ErrorProps? errorProps;
  final bool isTileDisabled;
  final bool isTileLoading;

  const UpiTile({
    Key? key,
    required this.upiTileConstants,
    required this.onTap,
    this.isTileDisabled = false,
    this.isTileLoading = false,
    this.errorProps,
  }) : super(key: key);

  @override
  State<UpiTile> createState() => _UpiTileState();
}

class _UpiTileState extends State<UpiTile> {
  void _handleTap() {
    widget.onTap();
  }

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: widget.isTileDisabled ? 0.5 : 1.0,
      child: GestureDetector(
        onTap: widget.isTileDisabled ? null : _handleTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    decoration: BoxDecoration(
                      color: colorF5F5F5,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: HybridImage(
                      imageUrl: widget.upiTileConstants.imageUrl,
                      height: 40,
                      width: 40,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (widget.upiTileConstants.title != null) ...[
                        AckoTextConfig.i.paragraphXSmall
                            .text(widget.upiTileConstants.title!),
                        SizedBox(
                          height: 4,
                        ),
                      ],
                      AckoTextConfig.i.labelMedium
                          .text(widget.upiTileConstants.subtitle),
                    ],
                  ),
                  const Spacer(),
                  widget.isTileLoading
                      ? const UiKitLoadingDots()
                      : const Icon(Icons.arrow_forward_rounded,
                          size: 20.0, color: color000000),
                ],
              ),
              const SizedBox(height: 4),
              Divider(
                thickness: 1,
                indent: 60,
              ),
              const SizedBox(height: 8),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomUpiIdWidget extends StatefulWidget {
  final VoidCallback onTap;
  final bool isTileLoading;
  final bool isTileDisabled;
  const CustomUpiIdWidget({
    Key? key,
    required this.onTap,
    this.isTileLoading = false,
    this.isTileDisabled = false,
  }) : super(key: key);

  @override
  State<CustomUpiIdWidget> createState() => _CustomUpiIdWidgetState();
}

class _CustomUpiIdWidgetState extends State<CustomUpiIdWidget> {
  void _handleTap() {
    widget.onTap();
  }

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: widget.isTileDisabled ? 0.5 : 1.0,
      child: GestureDetector(
        onTap: widget.isTileDisabled ? null : _handleTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    decoration: BoxDecoration(
                      color: colorF5F5F5,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: HybridImage(
                      imageUrl: AssetsConstants.upi_generic_img,
                      height: 40,
                      width: 40,
                    ),
                  ),
                  const SizedBox(width: 8),
                  AckoTextConfig.i.labelMedium
                      .text(ChallanAutomationConstants.addUpiId),
                  const Spacer(),
                  widget.isTileLoading
                      ? const UiKitLoadingDots()
                      : const Icon(Icons.arrow_forward_rounded,
                          size: 20.0, color: color000000),
                ],
              ),
              const SizedBox(height: 8),
            ],
          ),
        ),
      ),
    );
  }
}

class ChallanUpiErrorWidget extends StatelessWidget {
  final String errorMessage;
  const ChallanUpiErrorWidget({super.key, required this.errorMessage});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: colorFBEAEA,
        borderRadius: BorderRadius.circular(8.0),
      ),
      padding: const EdgeInsets.all(12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(width: 8),
          // Text
          Expanded(
            child: AckoTextConfig.i.paragraphSmall
                .text(errorMessage, textColor: color95221D),
          ),
        ],
      ),
    );
  }
}
