import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/challan_webview/enums/challan_webview_enums.dart';
import 'package:acko_flutter/feature/challan_webview/models/challan_keyword_model.dart';
import 'package:acko_flutter/feature/challan_webview/widgets/pay_through_upi_widget.dart';
import 'package:acko_flutter/feature/challan_webview/widgets/script_error_page.dart';
import 'package:acko_flutter/feature/challan_webview/widgets/show_info_widget.dart';
import 'package:acko_flutter/feature/challan_webview/widgets/text_input_widget.dart';
import 'package:acko_flutter/feature/challan_webview/widgets/timer_loading_screen_widget.dart';
import 'package:acko_flutter/feature/challan_webview/widgets/tracking_loading_screen.dart';
import 'package:acko_flutter/feature/challan_webview/widgets/upi_completed.dart';
import 'package:acko_flutter/feature/challan_webview/widgets/upi_in_progress.dart';
import 'package:design_module/design_module.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../challan_final_screen.dart';
import '../challan_web_view_bloc.dart';
import 'countdown_text_field.dart';
import 'custom_loading_screen.dart';

class UserInputWidget extends StatefulWidget {
  final Function(String) onSubmit;
  final ChallanKeywordModel challanKeywordModel;

  const UserInputWidget(
      {Key? key, required this.onSubmit, required this.challanKeywordModel})
      : super(key: key);

  @override
  _UserInputWidgetState createState() => _UserInputWidgetState();
}

class _UserInputWidgetState extends State<UserInputWidget> {
  String? userInput;
  final TextEditingController _textEditingController = TextEditingController();
  ChallanWebViewBloc? _bloc;

  @override
  void initState() {
    super.initState();
    _bloc = BlocProvider.of(context);
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 20,
        right: 20,
        bottom: 12,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            height: 100,
          ),
          Expanded(child: _buildResponseWidget(widget.challanKeywordModel)),
        ],
      ),
    );
  }

  Widget _buildResponseWidget(ChallanKeywordModel challanKeywordModel) {
    switch (challanKeywordModel.responseType) {
      case KeywordResponseType.otp_field:
        if (challanKeywordModel.modelProperties is OtpFieldProperties) {
          final otpProps =
              challanKeywordModel.modelProperties as OtpFieldProperties;
          return OtpTextField(
            countdownSeconds: otpProps.timer,
            textfieldLength: otpProps.textfieldLength,
            errorMessage: challanKeywordModel.error?.errorMessage,
            title: otpProps.title,
            subtitle: otpProps.subtitle,
            onSubmit: (String value) {
              widget.onSubmit(value);
            },
            helperScript: (HelperScript) {
              _bloc?.executeHelperScript(HelperScript);
            },
          );
        } else {
          return SizedBox.shrink();
        }

      case KeywordResponseType.textfield:
        if (challanKeywordModel.modelProperties is TextfieldProperties) {
          final textfieldProps =
              challanKeywordModel.modelProperties as TextfieldProperties;

          return TextInputWidget(
            title: textfieldProps.title,
            subtitle: textfieldProps.subtitle,
            onSubmit: (String value) {
              widget.onSubmit(value);
            },
          );
        } else {
          return SizedBox.shrink();
        }
      case KeywordResponseType.upi_in_progress:
        if (challanKeywordModel.modelProperties is UpiInProgressProperties) {
          final upiInProgressProps =
              challanKeywordModel.modelProperties as UpiInProgressProperties;
          return UpiInProgressWidget(
            timerInSeconds: upiInProgressProps.timerInSeconds,
            challanAmount: _bloc?.userFlowData.amountBreakup?.totalPayable,
          );
        } else {
          return SizedBox.shrink();
        }
      case KeywordResponseType.upi_completed:
        if (challanKeywordModel.modelProperties is UpiCompletedProperties) {
          final upiCompletedProps =
              challanKeywordModel.modelProperties as UpiCompletedProperties;
          return UpiCompleted(
            title: upiCompletedProps.title,
            paymentId: upiCompletedProps.paymentReferenceId,
          );
        } else {
          return SizedBox.shrink();
        }
      case KeywordResponseType.final_screen:
        if (challanKeywordModel.modelProperties is FinalScreenProperties) {
          final finalScreenProps =
              challanKeywordModel.modelProperties as FinalScreenProperties;
          return ChallanFinalScreen(
            helperScript: (HelperScript) {
              _bloc?.executeHelperScript(HelperScript);
            },
            finalScreenProps: finalScreenProps,
            challanKeywordModel: challanKeywordModel,
          );
        } else {
          return SizedBox.shrink();
        }
      case KeywordResponseType.script_error:
        if (challanKeywordModel.modelProperties is ScriptErrorProperties) {
          final scriptErrorProps =
              challanKeywordModel.modelProperties as ScriptErrorProperties;
          return ScriptErrorPage(
            shouldTryAgain: true,
            onErrorTapped: () => _bloc?.onScriptRetryButtonTapped(
                scriptErrorProps.errorType, scriptErrorProps.url),
            onSubmit: (String value) {
              widget.onSubmit(value);
            },
          );
        } else {
          return SizedBox.shrink();
        }
      case KeywordResponseType.pay_through_upi:
        if (challanKeywordModel.modelProperties is PayThroughUpiProperties) {
          final upiProps =
              challanKeywordModel.modelProperties as PayThroughUpiProperties;

          return PayThroughUpiWidget(
            title: upiProps.title,
            subtitle: upiProps.subtitle,
            onSubmit: (String value) {
              widget.onSubmit(value);
            },
            errorProps: challanKeywordModel.error,
            amount: upiProps.amount,
            userUpiIds: upiProps.upiIds,
          );
        } else {
          return SizedBox.shrink();
        }

      case KeywordResponseType.custom_loading_screen:
        if (challanKeywordModel.modelProperties
            is CustomLoadingScreenProperties) {
          final loadingScreenProps = challanKeywordModel.modelProperties
              as CustomLoadingScreenProperties;
          return CustomLoadingScreen(
            title: loadingScreenProps.title,
            subtitle: loadingScreenProps.subtitle,
            loadingType: loadingScreenProps.loadingType,
          );
        } else {
          return SizedBox.shrink();
        }

      case KeywordResponseType.tracking_loading_screen:
        if (challanKeywordModel.modelProperties
            is TrackingLoadingScreenProperties) {
          final trackingScreenProps = challanKeywordModel.modelProperties
              as TrackingLoadingScreenProperties;
          return TrackingLoadingScreen(
            screenProps: trackingScreenProps,
          );
        } else {
          return SizedBox.shrink();
        }

      case KeywordResponseType.timer_loading_screen:
        if (challanKeywordModel.modelProperties
            is TimerLoadingScreenProperties) {
          final timerLoadingScreenProps = challanKeywordModel.modelProperties
              as TimerLoadingScreenProperties;
          return TimerLoadingScreenWidget(
            messages: timerLoadingScreenProps.messages,
          );
        } else {
          return SizedBox.shrink();
        }

      case KeywordResponseType.show_info:
        if (challanKeywordModel.modelProperties is ShowInfoProperties) {
          final showInfoProps =
              challanKeywordModel.modelProperties as ShowInfoProperties;
          return ShowInfoWidget(
            title: showInfoProps.title,
            message: showInfoProps.message,
            onSubmit: (String value) {
              widget.onSubmit(value);
            },
          );
        } else {
          return SizedBox.shrink();
        }

      default:
        return SizedBox.shrink();
    }
  }

  Widget _buildErrorWidget(ErrorProps error) {
    return pSmallText.text(error.errorMessage, textColor: colorF75276);
  }
}
