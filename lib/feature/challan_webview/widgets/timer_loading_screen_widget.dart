import 'dart:async';

import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/acko_text_config.dart';
import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/material.dart';

import '../../../util/Utility.dart';
import '../models/challan_keyword_model.dart';

class TimerLoadingScreenWidget extends StatefulWidget {
  final List<TimerLoadingMessage> messages;
  const TimerLoadingScreenWidget({Key? key, required this.messages}) : super(key: key);

  @override
  State<TimerLoadingScreenWidget> createState() => _TimerLoadingScreenWidgetState();
}

class _TimerLoadingScreenWidgetState extends State<TimerLoadingScreenWidget> {
  int currentMessageIndex = 0;
  int remainingSeconds = 0;
  Timer? messageTimer;
  Timer? countdownTimer;

  @override
  void initState() {
    super.initState();
    AnalyticsTrackerManager.instance
        .sendEvent(event: PageLoadedConstants.APP_VIEW_PAGE, properties: {
      'platform': Util.getPlatform(),
      'page_name': 'connecting to RTO loader'
    });
    _startTimersForCurrentMessage();
  }

  void _startTimersForCurrentMessage() {
    messageTimer?.cancel();
    countdownTimer?.cancel();

    final currentMessage = widget.messages[currentMessageIndex];
    if (currentMessage.timer != null) {
      remainingSeconds = currentMessage.timer!;

      messageTimer = Timer(Duration(seconds: currentMessage.timer!), () {
        if (currentMessageIndex < widget.messages.length - 1) {
          setState(() {
            currentMessageIndex++;
          });
          _startTimersForCurrentMessage();
        }
      });

      countdownTimer = Timer.periodic(Duration(seconds: 1), (timer) {
        if (remainingSeconds > 0) {
          setState(() {
            remainingSeconds--;
          });
        } else {
          timer.cancel();
        }
      });
    }
  }

  @override
  void dispose() {
    messageTimer?.cancel();
    countdownTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final message = widget.messages[currentMessageIndex];

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          HybridImage(imageUrl: ChallanAutomationConstants.imageTrackingScreenEvaluation, height: 80, width: 80,),
          const SizedBox(height: 8),
          AckoTextConfig.i.headingSmall.text(message.title),
          const SizedBox(height: 8),
          AckoTextConfig.i.labelSmall.text(message.subtitle),
          const SizedBox(height: 24),
          if (message.timer != null && message.timer != 0)
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: colorF5F5F5,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  alignment: Alignment.center,
                  child: AckoTextConfig.i.headingXXSmall.text("00"),
                ),
                const SizedBox(width: 4),
                AckoTextConfig.i.headingXXSmall.text(":"),
                const SizedBox(width: 4),
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: colorF5F5F5,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  alignment: Alignment.center,
                  child: AckoTextConfig.i.headingXXSmall.text(remainingSeconds.toString().padLeft(2, "0")),
                ),
              ],
            ),
        ],
      ),
    );
  }
}
