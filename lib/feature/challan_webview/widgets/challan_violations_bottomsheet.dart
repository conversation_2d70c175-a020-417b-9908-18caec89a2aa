import 'package:acko_flutter/common/view/acko_text_config.dart';
import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:flutter/material.dart';

class ChallanViolationsBottomsheet extends StatelessWidget {
  final List<Map<String, String>> detailItems;
  const ChallanViolationsBottomsheet({
    super.key,
    required this.detailItems,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          AckoTextConfig.i.headingSmall.text("Challan details"),
          const SizedBox(height: 24),
          ...detailItems.asMap().entries.expand<Widget>((entry) {
            final idx = entry.key;
            final map = entry.value;
            final pair = map.entries.first;
            final key   = pair.key;
            final value = pair.value;

            final row = Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 4,
                  child: AckoTextConfig.i.labelSmall.text(
                    key,
                    maxLines: 4,
                  ),
                ),
                Expanded(
                  flex: 4,
                  child: AckoTextConfig.i.headingXXSmall.text(
                     value,
                    textAlign: TextAlign.end,
                    maxLines: 1,
                  ),
                ),
              ],
            );

            if (idx < detailItems.length - 1) {
              return [
                row,
                const SizedBox(height: 20),
                const Divider(thickness: 1),
                const SizedBox(height: 20),
              ];
            } else {
              return [row];
            }
          }).toList(),
          SizedBox(height: 16,),
          AckoDarkButtonFullWidth(text: ChallanAutomationConstants.okay, onTap: () {
            Navigator.pop(context);
          })
        ],
      ),
    );
  }
}
