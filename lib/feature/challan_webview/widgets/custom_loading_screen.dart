import 'package:acko_flutter/feature/challan_webview/enums/challan_webview_enums.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/uikit/widgets/loaders/uikit_loading_dots.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class CustomLoadingScreen extends StatelessWidget {
  final String title;
  final String? subtitle;
  final ChallanWebviewLoadingType loadingType;
  const CustomLoadingScreen(
      {super.key,
      required this.title,
      this.subtitle,
      required this.loadingType});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (loadingType == ChallanWebviewLoadingType.threeDotLoader)
              const UiKitLoadingDots(dotSize: 8.0)
            else
              Lottie.asset('assets/anim/updated_loading_anim.json',
                  height: 100, width: 100),
            hSmallText.text(title, textAlign: TextAlign.center),
            SizedBox(
              height: 8,
            ),
            if (subtitle != null)
              pSmallText.text(subtitle!, textAlign: TextAlign.center),
          ],
        ),
      ),
    );
  }
}
