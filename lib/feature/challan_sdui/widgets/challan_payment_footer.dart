import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/challan/bloc/challan_automation_confirmation_cubit.dart';
import 'package:acko_flutter/feature/challan/view/challan_automation_confirmation.dart';
import 'package:acko_flutter/feature/challan/view/challan_payment_confirmation_bottomsheet.dart';
import 'package:acko_flutter/feature/challan_webview/enums/challan_webview_enums.dart';
import 'package:acko_flutter/feature/challan_webview/utils/challan_webview_utility.dart';
import 'package:acko_flutter/feature/vas-sdui/animated_digit.dart';
import 'package:acko_flutter/feature/challan_sdui/cubit/challan_cubit.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_logger/acko_logger.dart';
import 'package:acko_logger/events/info/app_debug_info.dart';
import 'package:analytics/analytics.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:design_module/design_module.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';
import 'package:sprintf/sprintf.dart';
import 'package:utilities/constants/constants.dart';
import '../model/challan_layout_data.dart';
import 'challan_payment_bottom_sheet.dart';

class ChallanFooter extends StatefulWidget {
  final String? stateKey;
  final ChallanFooterConfig footerConfig;
  final String? searchSource;
  final bool paytoSource;

  const ChallanFooter({
    required this.footerConfig,
    this.stateKey,
    this.searchSource,
    this.paytoSource = false,
  });

  @override
  State<ChallanFooter> createState() => _ChallanFooterState();
}

class _ChallanFooterState extends State<ChallanFooter>
    with SingleTickerProviderStateMixin {
  late ChallanCubit cubit;
  late final AnimationController _slideController;
  late final Animation<Offset> _slideAnimation;
  bool _wasEmpty = true;

  @override
  void initState() {
    cubit = context.read<SduiParentCubit>() as ChallanCubit;
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOut,
    ));
    _slideController.forward();
    super.initState();
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  _payViaChallanAutomation(String redirectionUrl) {
    final arguments = cubit.getRtoPaymentParams(
      regNumber: widget.stateKey!,
      searchSource: widget.searchSource,
      redirectionUrl: ChallanWebviewUtility.constructChallanUrl(
          redirectionUrl, [UrlAppendAction.hideWebviewAppbar]),
    );

    AckoLoggerManager.instance.logInfo(
        event: AppDebugInfoEvent(
      page: 'challan_journey',
      infoMessage: '_payViaChallanAutomation called',
      journey: 'challan_payment_automation',
      data: {
        'arguments': arguments,
      },
    ));

    context.showAckoModalBottomSheet(
        child: Padding(
      padding: const EdgeInsets.all(20.0),
      child: BlocProvider(
          create: (context) => ChallanAutomationConfirmationCubit(),
          child: ChallanAutomationConfirmationBottomsheet(
            onNext: () {
              Navigator.pushNamed(
              context,
              Routes.CHALLAN_WEB_PAGE,
              arguments: arguments,
            );
          },
          searchSource: arguments['violation_location'],
          userPhoneNumber: arguments['phone_number'],
        ),
      ),
    ));
  }

  _payViaRTO(String redirectionUrl) async {
    final arguments = cubit.getRtoPaymentParams(
      regNumber: widget.stateKey!,
      searchSource: widget.searchSource,
      redirectionUrl: redirectionUrl,
    );

    AckoLoggerManager.instance.logInfo(
        event: AppDebugInfoEvent(
      page: 'challan_journey',
      infoMessage: '_payViaRTO called',
      journey: 'challan_rto_payment',
      data: {
        'arguments': arguments,
      },
    ));

    await Clipboard.setData(ClipboardData(text: arguments['clipboard_data']));
    Navigator.pushNamed(context, Routes.WEB_PAGE_V2, arguments: arguments)
        .then((value) {
      context.showAckoModalBottomSheet(
        child: BlocProvider.value(
          value: cubit,
          child: ChallanPaymentConfirmationBottomsheet(
            noticeNumbers: arguments['violation_number'],
            registrationNumber: arguments['registration_number'],
            reloadChallan: () {
              cubit.updateVehicleChallanState(widget.stateKey!,
                  forceRefresh: true);
            },
            searchSource:
                arguments['violation_location'] ?? widget.searchSource ?? '',
          ),
        ),
      );
    });
  }

  _onCtaTap() {
    AnalyticsTrackerManager.instance.sendEvent(
      event: TapConstants.TAP_BTN_CHALLANS_PAY_NOW,
      properties: {
        'registration_number': widget.stateKey,
        'count': updatedTotalChallanCount,
        'booking_amount': updatedTotalAmount
      },
    );

    final bottomSheet = cubit.getBottomSheetForAsset(widget.stateKey ?? '');

    if (!widget.paytoSource &&
        cubit.assethasMultipleSearchSources(widget.stateKey!) &&
        bottomSheet != null) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder: (context) => FractionallySizedBox(
          heightFactor: 0.85,
          child: BlocProvider.value(
            value: cubit as SduiParentCubit,
            child: ChallanPaymentBottomSheet(
              footerConfig: widget.footerConfig,
              bottomsheet: bottomSheet,
            ),
          ),
        ),
      );
    } else {
      final redirection = cubit.getChallanAutomationAvailability(
        widget.stateKey!,
        widget.searchSource,
      );
      if (redirection.isAutomationAvailable) {
        _payViaChallanAutomation(redirection.redirectionUrl);
      } else {
        _payViaRTO(redirection.redirectionUrl);
      }
    }
  }

  /// Only for tracking analytics
  double? updatedTotalAmount;
  int? updatedTotalChallanCount;

  (bool isEmpty, double totalAmount, int totalChallan)
      _getChallanAmountOrIsEmpty(UpdateFromParentState state) {
    final challanList = state.state[widget.stateKey] as List?;
    final isEmpty = challanList == null ||
        challanList.isEmpty ||
        (widget.searchSource != null &&
            !challanList.any((e) => e['searchSource'] == widget.searchSource));

    if (isEmpty)
      return (isEmpty, 0.0, 0);
    else {
      double totalAmount = 0;
      int filteredChallanCount = 0;
      challanList.forEach((e) {
        if (widget.searchSource == null ||
            e['searchSource'] == widget.searchSource) {
          totalAmount += e['value'];
          filteredChallanCount++;
        }
      });

      updatedTotalAmount = totalAmount;
      updatedTotalChallanCount = filteredChallanCount;
      return (false, totalAmount, filteredChallanCount);
    }
  }

  String getTrailingBasedOnCount(int count) {
    return count > 1 ? 's' : '';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(
        top: 12,
        left: 16,
        right: 16,
        bottom: 8,
      ),
      decoration: BoxDecoration(
        color: colorFFFFFF,
        boxShadow: [
          BoxShadow(
            color: const Color(0x0F36354C),
            offset: const Offset(0, -4),
            blurRadius: 8,
            spreadRadius: -2,
          ),
        ],
      ),
      child: BlocConsumer<ChallanCubit, SduiParentState>(
        bloc: cubit,
        listener: (context, state) {
          if (state is UpdateFromParentState) {
            final (isEmpty, totalAmount, challanCount) =
                _getChallanAmountOrIsEmpty(state);
            AnalyticsTrackerManager.instance.sendEvent(
              event: TapConstants.TAP_BTN_CHALLANS_CHECK_CHALLAN_STATUS,
              properties: {
                'from_page': "challan details",
                'count': challanCount,
                'booking_amount': totalAmount,
              },
            );
          }
        },
        buildWhen: (previous, current) => current is UpdateFromParentState,
        builder: (context, state) {
          if (state is UpdateFromParentState) {
            final (isEmpty, totalAmount, challanCount) =
                _getChallanAmountOrIsEmpty(state);

            if (isEmpty) {
              _wasEmpty = true;
              return SizedBox.shrink();
            }

            if (_wasEmpty) {
              _slideController.reset();
              _slideController.forward();
              _wasEmpty = false;
            }

            return SlideTransition(
              position: _slideAnimation,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (widget.footerConfig.title != null)
                        _TotalChallanAmount(
                          title: widget.footerConfig.title,
                          totalAmount: totalAmount,
                        )
                      else
                        const SizedBox.shrink(),
                      if (widget.footerConfig.subtitle != null)
                        SDUIText(
                          value: sprintf(
                              '${widget.footerConfig.subtitle!.value ?? 'Total for %d challan'}${getTrailingBasedOnCount(challanCount)}',
                              [challanCount]),
                          textStyle: widget.footerConfig.subtitle!.textStyle ??
                              'pSmall',
                          textColor: widget.footerConfig.subtitle!.textColor ??
                              Colors.grey,
                        )
                      else
                        const SizedBox.shrink(),
                    ],
                  ),
                  if (widget.footerConfig.cta != null)
                    SDUIButton(
                      onTap: _onCtaTap,
                      borderColor: widget.footerConfig.cta?.borderColor,
                      icon: widget.footerConfig.cta?.icon,
                      title:
                          '${widget.footerConfig.cta?.title ?? 'Pay challan'}${getTrailingBasedOnCount(challanCount)}',
                      type: widget.footerConfig.cta?.type,
                    ),
                ],
              ),
            );
          }
          return SizedBox.shrink();
        },
      ),
    );
  }
}

class _TotalChallanAmount extends StatelessWidget {
  const _TotalChallanAmount({
    super.key,
    required this.title,
    required this.totalAmount,
  });

  final SDUIText? title;
  final double totalAmount;

  @override
  Widget build(BuildContext context) {
    String amount = totalAmount.formatPriceWithCommas();
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SDUIText(
          value: '\u{20B9}',
          textStyle: title!.textStyle ?? 'lLarge',
          textColor: title!.textColor,
          margin: EdgeInsets.only(top: 2),
        ),
        ...sprintf(
          '%s',
          [amount],
        )
            .split('')
            .asMap()
            .entries
            .map(
              (entry) => AnimatedDigit(
                key: ValueKey(entry.key),
                oldValue: entry.value,
                newValue: entry.value,
                index: entry.key,
                textStyle: title!.textStyle ?? 'lLarge',
                textColor: title!.textColor,
              ),
            )
            .toList(),
      ],
    );
  }
}
