// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';
import 'dart:io';
import 'package:acko_flutter/feature/challan/challan_enums.dart';
import 'package:acko_flutter/feature/challan_webview/challan_automation_repository.dart';
import 'package:acko_flutter/feature/challan_webview/models/challan_automation_misc_models.dart';
import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:acko_logger/acko_logger.dart';
import 'package:acko_logger/events/info/app_debug_info.dart';
import 'package:sdui/sdui.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/remote_config/remote_config.dart';

import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_flutter/feature/challan_webview/models/automation_availability_status_model.dart';
import 'package:acko_flutter/util/extensions.dart';

import '../../vas-sdui/bloc/vas_cubit.dart';
import '../model/challan_layout_data.dart';

class ChallanCubit extends SduiParentCubit {
  final Map<String, dynamic>? challaDataStoreJson;
  final String? registrationNumber;
  ChallanCubit({this.challaDataStoreJson, this.registrationNumber})
      : super(ChallanLoading()) {
    if (challaDataStoreJson == null) {
      fetchJourneyData();
    } else {
      sduiJson = challaDataStoreJson;
      parseJson();
    }
    initChallanMetadata();
  }

  ChallanLayoutData? layoutData;
  Map<String, dynamic> challanRedirectionUrls = {};
  String? userPhoneNumber;
  Set<String> refreshedTabs = {};
  int? currentIndex;

  final BaseRepository _repository = BaseRepository();
  final ChallanAutomationRepository _challanAutomationRepo =
      ChallanAutomationRepository();

  Future<void> fetchJourneyData() async {
    emit(ChallanLoading());
    final result = (await _repository.getResponse(
        '/vas/journey?type=CHALLAN&registrationNumber=$registrationNumber'));

    if (result.data == null) {
      emit(ChallanError(error: result.error?.error));
    } else {
      sduiJson = result.data;
      emit(VasJourneyLoaded());
    }
  }

  void initChallanMetadata() async {
    final String urlsMetadata = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.challanAutomationStatus);
    challanRedirectionUrls = jsonDecode(urlsMetadata);
    userPhoneNumber =
        await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);
  }

  Future<void> updateVehicleChallanState(String assetNumber,
      {bool forceRefresh = false}) async {
    AckoLoggerManager.instance.logInfo(
        event: AppDebugInfoEvent(
            page: 'challan_journey',
            infoMessage: 'updateVehicleChallanState called',
            journey: 'challan_journey',
            data: {
          'registrationNumber': assetNumber,
        }));

    if (forceRefresh || !refreshedTabs.contains(assetNumber)) {
      refreshedTabs.add(assetNumber);
      final result = (await _repository.getResponse(
              '/vas/journey?type=CHALLAN&registrationNumber=$assetNumber&tabRefresh=true'))
          .data;
      result?.forEach((key, value) {
        updateJsonByKey(key, value);
      });
    }
  }

  Future<void> updateChallanPaymentOrder({
    required List<String> noticeNumbers,
    required String referenceNumber,
    required String registrationNumber,
    required String source,
    required ChallanPaymentStatus ackoPaymentStatus,
  }) async {
    final ChallanSearchSource searchSource =
        ChallanSearchSource.fromString(source);
    String? userPhoneNumber =
        await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);

    await _challanAutomationRepo.sendPaymentStatus(
      noticeNumbers: noticeNumbers,
      referenceNumber: referenceNumber,
      registrationNumber: registrationNumber,
      source: searchSource,
      ackoPaymentStatus: ackoPaymentStatus,
      phoneNumber: userPhoneNumber!,
      shouldSendHistroyProps: false,
    );
  }

  void fetchCurrentState() {
    emit(UpdateFromParentState(stateVariables));
  }

  int? getDefaultTabIndex() {
    if (currentIndex != null) {
      return currentIndex;
    }
    if (registrationNumber == null ||
        sduiJson?['listing']?['layout']?['body'] == null) {
      return null;
    }

    final body = sduiJson!['listing']['layout']['body'];
    final index = body is Map<String, dynamic>
        ? body.keys.toList().indexWhere(
            (key) => key.toLowerCase() == registrationNumber!.toLowerCase())
        : null;

    if ((index ?? -1) >= 0) {
      return index;
    } else
      return null;
  }

  changeDefaultTabIndex(int index) {
    currentIndex = index;
  }

  void parseJson({bool showLoader = false}) async {
    final initActions = sduiJson?['listing']?['initActions'];
    if (initActions != null && (initActions as List).isNotNullOrEmpty) {
      final List<SDUIAction> actions = List<SDUIAction>.from(
        (initActions).map((action) => SDUIAction().fromJson(action)),
      );
      emit(ChallanRunInitActions(actions));
      await Future.delayed(Duration(seconds: 2));
    }

    if (showLoader) {
      final loaderData = sduiJson?['listing']?['loading'] != null
          ? ChallanLoaderLayout.fromJson(sduiJson?['listing']?['loading'])
          : null;

      if (loaderData != null) {
        emit(ChallanLoader(loaderData));
        await Future.delayed(Duration(seconds: 2));
      }
    }

    layoutData = ChallanLayoutData.fromJson(sduiJson?['listing']?['layout']);
    if (layoutData != null) {
      emit(ChallanDataLoaded(layoutData!));
    } else {
      emit(ChallanError());
    }
  }

  /// Updates the state variables based on the provided arguments.
  ///
  /// This method takes a [Map] of arguments and updates the internal [stateVariables].
  /// For each key-value pair in the arguments:
  /// - If the key exists in stateVariables and both values are Lists,
  ///   it merges the lists while avoiding duplicates based on 'id' field
  /// - If the key exists but values aren't lists, or key doesn't exist,
  ///   it simply updates/adds the value
  ///
  /// Parameters:
  /// Arguments to update the ChallanState, maintaining key-value pairs for:
  /// - Registration number to challan amount mapping
  /// - Registration number to challan metadata mapping (like challan type, date, location etc.)
  /// - Notice number (id) to registration number mapping for challan identification
  ///   arguments: Map<String, dynamic> - Key-value pairs to update the state with
  @override
  void updateState(Map<String, dynamic> arguments) {
    arguments.forEach((key, value) {
      if (stateVariables.containsKey(key)) {
        final existingValue = stateVariables[key];
        if (existingValue is List && value is List) {
          // Only add items that don't already exist (based on id)
          final newItems = value
              .where((newItem) => !existingValue
                  .any((existingItem) => existingItem['id'] == newItem['id']))
              .toList();
          stateVariables[key] = [...existingValue, ...newItems];
        } else {
          stateVariables[key] = value;
        }
      } else {
        stateVariables[key] = value;
      }
    });

    emit(UpdateFromParentState(stateVariables));
  }

  @override
  void executeCustomAction(Map<String, dynamic>? actionData) async {
    emit(ChallanLoading());

    final String? missingState = actionData?['missing_state'];

    AckoLoggerManager.instance.logInfo(
        event: AppDebugInfoEvent(
            page: 'challan_journey',
            infoMessage: 'executeCustomAction called',
            journey: 'challan_journey',
            data: {
          'actionData': actionData,
        }));

    if (missingState.isNotNullOrEmpty) {
      final result = (await _repository.getResponse(
          '/vas/journey?type=CHALLAN&registrationNumber=$registrationNumber&extraSearchSource=${actionData?['missing_state']}&tabRefresh=true'));
      result.data?.forEach((key, value) {
        updateJsonByKey(key, value);
      });
    }
  }

  /// Removes specified elements from the state based on provided arguments.
  ///
  /// Takes a [Map] of arguments where each key-value pair represents elements to be removed.
  /// For list type values, removes elements from existing state list where 'id' matches.
  ///
  /// Parameters:
  ///   arguments: Map<String, dynamic> - Key-value pairs of elements to remove
  ///
  /// Example:
  /// ```dart
  /// removeFromState({
  ///   'items': [{'id': 1}, {'id': 2}] // Will remove items with id 1 and 2
  /// });
  /// ```
  /// After removal, emits [UpdateFromParentState] with updated state variables.
  @override
  void removeFromState(Map<String, dynamic> arguments) {
    arguments.forEach((key, value) {
      if (stateVariables.containsKey(key)) {
        final existingValue = stateVariables[key];
        if (existingValue is List && value is List) {
          stateVariables[key] = existingValue
              .where((element) => !value.any((v) => v['id'] == element['id']))
              .toList();
        }
      }
    });
    emit(UpdateFromParentState(stateVariables));
  }

  ChallanBottomSheet? getBottomSheetForAsset(String stateKey) {
    return layoutData?.body?[stateKey]?.bottomsheet;
  }

  /// Returns a list of challans filtered by asset number and search source.
  ///
  /// Takes an [assetNumber] string to identify the asset and a [searchSource] string
  /// to filter challans by their source.
  ///
  /// Returns:
  /// * A filtered list of challans as List<Map<String, dynamic>> if challans exist
  /// * An empty list if no challans are found for the given asset number
  List<Map<String, dynamic>> getChallansBySearchSource(
      String assetNumber, String searchSource) {
    final challans = stateVariables[assetNumber] as List;
    if (challans.isNotNullOrEmpty) {
      final c = challans
          .cast<Map<String, dynamic>>()
          .where((e) => e['searchSource'] == searchSource)
          .toList();
      return c;
    } else {
      return [];
    }
  }

  Set<String?> getChallanSearchSources(String assetNumber) {
    final challans = stateVariables[assetNumber] as List?;

    return challans!
        .cast<Map<String, dynamic>>()
        .map((e) => e['searchSource']?.toString())
        .where((source) => source != null)
        .toSet();
  }

  bool assethasMultipleSearchSources(String assetNumber) {
    final searchSources = getChallanSearchSources(assetNumber);
    return searchSources.length > 1;
  }

  /// Filters tabs based on their searchSource and presence in stateVariables
  /// Returns a filtered list of tabs that have valid searchSources and exist in stateVariables
  List<Map<String, dynamic>> filterTabsBySearchSource(
      List<Map<String, dynamic>>? tabs, String assetNumber) {
    final searchSources = getChallanSearchSources(assetNumber);

    if (tabs == null) return [];
    return tabs.where((tab) {
      final searchSource = tab['searchSource']?.toString();
      return searchSource != null && searchSources.contains(searchSource);
    }).toList();
  }

  ChallanConfig getChallanAutomationAvailability(
      String regNumber, String? searchSource) {
    if (searchSource.isNullOrEmpty) {
      final challans = stateVariables[regNumber] as List?;
      if (challans?.isNotEmpty == true) {
        searchSource = challans?.first['searchSource']?.toString() ?? '';
      }
    }
    String? automationStatus = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.challanAutomationStatusV2);
    if (automationStatus.isNullOrEmpty) {
      automationStatus =
          ChallanAutomationConstants.getDefaultChallanAutomationStatus();
    }

    final Map<String, dynamic> configMap = jsonDecode(automationStatus!);
    final data = configMap[searchSource!.trim().toLowerCase()];
    final config = AutomationAvailabilityStatusModel.fromJson(data);

    final int minVersion = Platform.isAndroid
        ? int.parse(config.minAndroidVersion)
        : int.parse(config.minIosVersion);

    bool isAutomationAvailable =
        Constants.BUILD_NUMBER! >= minVersion && config.isAutomationActive;

    return ChallanConfig(
        isAutomationAvailable: isAutomationAvailable,
        redirectionUrl: config.redirectionUrl);
  }

  Map<String, dynamic> getRtoPaymentParams(
      {required String regNumber,
      String? searchSource,
      required String redirectionUrl}) {
    final encodedStatesNeedingRegNum = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.CHALLAN_REGNUM_STATES);

    if (searchSource.isNullOrEmpty) {
      final challans = stateVariables[regNumber] as List?;
      if (challans?.isNotEmpty == true) {
        searchSource = challans?.first['searchSource']?.toString() ?? '';
      }
    }

    final Map<String, dynamic>? statesNeedingRegNum =
        encodedStatesNeedingRegNum != null
            ? jsonDecode(encodedStatesNeedingRegNum)
            : null;

    final clipBoardData = (statesNeedingRegNum != null &&
            statesNeedingRegNum.containsKey(searchSource))
        ? regNumber
        : '';

    List<String> violationIds = [];
    final challans = getChallansBySearchSource(regNumber, searchSource!);
    if (challans.isNotEmpty) {
      violationIds = challans
          .map((challan) => challan['id']?.toString() ?? '')
          .where((id) => id.isNotEmpty)
          .toList();
    }

    final routeArguments = {
      'violation_location': searchSource,
      'violation_number': violationIds,
      'registration_number': regNumber,
      'phone_number': userPhoneNumber,
      'phoneNumber': userPhoneNumber,
      'url': redirectionUrl,
      'hide_header': false,
      'clipboard_data': clipBoardData,
    };

    return routeArguments;
  }
}

class ChallanLoading extends VasState {}

class ChallanRunInitActions extends VasState {
  final List<SDUIAction> actions;
  ChallanRunInitActions(this.actions);
}

class ChallanError extends VasState {
  Object? error;
  ChallanError({
    this.error,
  });
}

class ChallanLoader extends VasState {
  ChallanLoaderLayout data;
  ChallanLoader(
    this.data,
  );
}

class ChallanDataLoaded extends VasState {
  ChallanLayoutData data;
  ChallanDataLoaded(this.data);
}
