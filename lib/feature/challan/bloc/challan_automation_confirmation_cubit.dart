import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/challan/challan_enums.dart';
import 'package:acko_flutter/feature/challan_webview/models/challan_automation_misc_models.dart';
import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

class ChallanAutomationConfirmationCubit
    extends AckoSafeCubit<ChallanAutomationConfirmationState> {
  ChallanAutomationConfirmationCubit()
      : super(ChallanAutomationConfirmationLoading());

  void loadConfirmationContent(String searchSource, String phoneNumber) {
    ChallanSearchSource searchSourceEnum =
        ChallanSearchSource.forAutomationFromString(searchSource);
    final confirmationProps = switch (searchSourceEnum) {
      ChallanSearchSource.KA => ConfirmationBottomsheetProps(
          title: ChallanAutomationConstants.payingRTO(
              ChallanAutomationConstants.karnatakaRto),
          subtitle:
              ChallanAutomationConstants.generateOtpInstruction(phoneNumber),
          buttonText: ChallanAutomationConstants.continueWord),
      ChallanSearchSource.TS => ConfirmationBottomsheetProps(
          title: ChallanAutomationConstants.payingRTO(
              ChallanAutomationConstants.telanganaRto),
          subtitle:
              ChallanAutomationConstants.generateOtpInstruction(phoneNumber),
          buttonText: ChallanAutomationConstants.continueWord),
      ChallanSearchSource.MH => ConfirmationBottomsheetProps(
          title: ChallanAutomationConstants.payingRTO(
              ChallanAutomationConstants.maharashtraRto),
          subtitle: ChallanAutomationConstants.secureDataPassingMessage,
          buttonText: ChallanAutomationConstants.continueWord),
      _ => ConfirmationBottomsheetProps(
          title: ChallanAutomationConstants.redirectedToGovernmentWebsite,
          subtitle: ChallanAutomationConstants.directlyPayingTheGovernment,
          buttonText: ok,
        ),
    };
    emit(ChallanAutomationConfirmationLoaded(
      title: confirmationProps.title,
      subtitle: confirmationProps.subtitle,
      buttonText: confirmationProps.buttonText,
    ));
  }
}

abstract class ChallanAutomationConfirmationState {}

class ChallanAutomationConfirmationLoading
    extends ChallanAutomationConfirmationState {}

class ChallanAutomationConfirmationLoaded
    extends ChallanAutomationConfirmationState {
  final String title;
  final String subtitle;
  final String buttonText;

  ChallanAutomationConfirmationLoaded({
    required this.title,
    required this.subtitle,
    required this.buttonText,
  });
}

class ChallanAutomationConfirmationError
    extends ChallanAutomationConfirmationState {}
