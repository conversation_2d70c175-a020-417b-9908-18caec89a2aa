import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:utilities/state_provider/StateProvider.dart';
import '../../../common/view/acko_text_config.dart';
import '../../../util/Utility.dart';
import '../../challan_sdui/cubit/challan_cubit.dart';
import '../../challan_webview/models/challan_automation_misc_models.dart';

class ChallanPaymentConfirmationBottomsheet extends StatefulWidget {
  final VoidCallback reloadChallan;
  final String registrationNumber;
  final List<String> noticeNumbers;
  final String searchSource;
  const ChallanPaymentConfirmationBottomsheet(
      {Key? key,
      required this.reloadChallan,
      required this.searchSource,
      required this.registrationNumber,
      required this.noticeNumbers})
      : super(key: key);

  @override
  State<ChallanPaymentConfirmationBottomsheet> createState() =>
      _ChallanPaymentConfirmationBottomsheetState();
}

class _ChallanPaymentConfirmationBottomsheetState
    extends State<ChallanPaymentConfirmationBottomsheet> {
  bool _hasConfirmed = false;

  PaymentConfirmationContent get _content => _hasConfirmed
      ? PaymentConfirmationContent(
          title: 'Thanks for letting us know',
          subtitle: ChallanAutomationConstants.rtoFlowPaymentProcessingMessage,
          primaryButtonText: 'Okay',
          secondaryButtonText: null,
        )
      : PaymentConfirmationContent(
          title: ChallanAutomationConstants.didYouCompletePayment,
          subtitle: ChallanAutomationConstants.weNeedToUpdateStatus,
          primaryButtonText: 'Yes',
          secondaryButtonText: 'No',
        );

  void _onPrimaryTap() {
    AnalyticsTrackerManager.instance.sendEvent(
      event: TapConstants.TAP_BTN_INTERACTION,
      properties: {
        'journey': 'challan',
        'type': 'Complete Payment',
        'cta_text': _content.primaryButtonText.toLowerCase(),
        'from_page': widget.searchSource,
      },
    );

    if (_hasConfirmed) {
      Navigator.pop(context);
    } else {
      context.read<ChallanCubit>().updateChallanPaymentOrder(
            noticeNumbers: widget.noticeNumbers,
            referenceNumber: DateTime.now().millisecondsSinceEpoch.toString(),
            registrationNumber: widget.registrationNumber,
            source: widget.searchSource,
            ackoPaymentStatus: ChallanPaymentStatus.PAYMENT_LOCKED,
          );

      context.read<ChallanCubit>().removeFromState({
        '${widget.registrationNumber}':
            widget.noticeNumbers.map((notice) => {'id': notice}).toList(),
      });

      widget.reloadChallan();
      setState(() => _hasConfirmed = true);
    }
  }

  void _onSecondaryTap() {
    AnalyticsTrackerManager.instance.sendEvent(
      event: TapConstants.TAP_BTN_INTERACTION,
      properties: {
        'journey': 'challan',
        'type': 'Complete Payment',
        'cta_text': _content.secondaryButtonText!.toLowerCase(),
        'from_page': widget.searchSource,
      },
    );

    Navigator.pop(context);
  }

  initState() {
    super.initState();
    AnalyticsTrackerManager.instance.sendEvent(
      event: PageLoadedConstants.APP_VIEW_PAGE,
      properties: {
        'platform': Util.getPlatform(),
        'page_name': 'Were you able to complete payment'
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    StateProvider _stateProvider = StateProvider();
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 14),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          HybridImage(
            imageUrl: ChallanAutomationConstants.imageDocumentMissing,
            height: 80,
            width: 80,
          ),
          SizedBox(
            height: 16,
          ),
          AckoTextConfig.i.headingSmall
              .text(_content.title, textAlign: TextAlign.center),
          SizedBox(
            height: 8,
          ),
          AckoTextConfig.i.paragraphSmall
              .text(_content.subtitle, textAlign: TextAlign.center),
          SizedBox(
            height: 24,
          ),
          Row(
            children: [
              if (_content.secondaryButtonText != null)
                Flexible(
                  child: AckoSecondaryButtonFullWidth(
                    text: _content.secondaryButtonText!,
                    onTap: _onSecondaryTap,
                  ),
                ),
              if (_content.secondaryButtonText != null)
                const SizedBox(width: 16),
              Flexible(
                child: AckoDarkButtonFullWidth(
                  text: _content.primaryButtonText,
                  onTap: _onPrimaryTap,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
