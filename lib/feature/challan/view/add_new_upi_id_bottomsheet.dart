import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:design_module/uikit/widgets/button/uikit_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../common/view/acko_text_config.dart';
import '../../challan_webview/challan_web_view_bloc.dart';
import '../../challan_webview/widgets/save_upi_id_component.dart';

class AddNewUpiIdBottomsheet extends StatefulWidget {
  final void Function(String value)? onSubmit;

  const AddNewUpiIdBottomsheet({Key? key, this.onSubmit}) : super(key: key);

  @override
  State<AddNewUpiIdBottomsheet> createState() => _AddNewUpiIdBottomsheetState();
}

class _AddNewUpiIdBottomsheetState extends State<AddNewUpiIdBottomsheet> {
  final TextEditingController _controller = TextEditingController();
  ChallanWebViewBloc? _bloc;

  @override
  void initState() {
    _bloc = BlocProvider.of(context);
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 20, left: 20, right: 20, bottom: MediaQuery.of(context).viewInsets.bottom+20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AckoTextConfig.i.headingSmall.text(ChallanAutomationConstants.addUpiId),
          const SizedBox(height: 20),
          TextField(
            autofocus: true,
            controller: _controller,
            decoration: InputDecoration(
              hintText: ChallanAutomationConstants.enterUpiId,
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          SaveUpiIdComponent(
            onCheckboxTicked: (bool shouldSaveUpiId) {
              _bloc?.userFlowData.saveUserUpiID = shouldSaveUpiId;
            },),
          const SizedBox(height: 12,),
          ValueListenableBuilder(
            valueListenable: _controller,
            builder: (context, value, child) {
              final hasText = value.text.trim().isNotEmpty;
              return AckoDarkButtonFullWidth(
                text: ChallanAutomationConstants.payNow,
                buttonState: hasText ? UIKitButtonState.active : UIKitButtonState.disabled,
                onTap: () {
                  widget.onSubmit?.call(_controller.text);
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ],
      ),
    );
  }
}
