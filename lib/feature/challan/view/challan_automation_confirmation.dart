import 'package:acko_flutter/common/view/acko_text_config.dart';
import 'package:acko_flutter/feature/challan/bloc/challan_automation_confirmation_cubit.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:utilities/constants/constants.dart';

import '../../../util/Utility.dart';

class ChallanAutomationConfirmationBottomsheet extends StatefulWidget {
  final String userPhoneNumber;
  final String searchSource;
  final VoidCallback onNext;
  const ChallanAutomationConfirmationBottomsheet({
    Key? key,
    required this.userPhoneNumber,
    required this.searchSource,
    required this.onNext,
  }) : super(key: key);

  @override
  State<ChallanAutomationConfirmationBottomsheet> createState() =>
      _ChallanAutomationConfirmationBottomsheetState();
}

class _ChallanAutomationConfirmationBottomsheetState
    extends State<ChallanAutomationConfirmationBottomsheet> {
  final _cubit = ChallanAutomationConfirmationCubit();
  @override
  initState() {
    super.initState();
    _cubit.loadConfirmationContent(widget.searchSource, widget.userPhoneNumber);
    AnalyticsTrackerManager.instance.sendEvent(
        event: PageLoadedConstants.APP_VIEW_PAGE,
        properties: {
          'platform': Util.getPlatform(),
          'page_name': 'pay directly to RTO'
        });
  }

  _onCtaTapped() {
    Navigator.pop(context);
    widget.onNext();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          HybridImage(
            imageUrl: AssetsConstants.pay_money,
            height: 80,
            width: 80,
          ),
          SizedBox(
            height: 20,
          ),
          BlocBuilder<ChallanAutomationConfirmationCubit,
              ChallanAutomationConfirmationState>(
            bloc: _cubit,
            builder: (context, state) {
              if (state is ChallanAutomationConfirmationLoaded) {
                return Column(
                  children: [
                    AckoTextConfig.i.headingSmall
                        .text(state.title, textAlign: TextAlign.center),
                    SizedBox(
                      height: 10,
                    ),
                    AckoTextConfig.i.paragraphSmall
                        .text(state.subtitle, textAlign: TextAlign.center),
                    SizedBox(
                      height: 20,
                    ),
                    AckoDarkButtonFullWidth(
                        text: state.buttonText, onTap: _onCtaTapped)
                  ],
                );
              } else if (state is ChallanAutomationConfirmationLoading) {
                return const Center(child: CircularProgressIndicator());
              } else {
                return const SizedBox.shrink();
              }
            },
          ),
        ],
      ),
    );
  }
}
