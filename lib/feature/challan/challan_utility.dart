
import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/challan_webview/utils/challan_webview_utility.dart';
import 'package:acko_flutter/feature/challan_webview/utils/constants.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:utilities/constants/constants.dart';

import '../../common/util/strings.dart';
import '../challan_webview/models/challan_automation_misc_models.dart';
import '../challan_webview/widgets/pay_through_upi_widget.dart';
import 'challan_enums.dart';

class ChallanUtility {
  static PaymentStatusModel? getPaymentStatusFromString(String? paymentStatus) {
    if (paymentStatus.isNullOrEmpty) {
      return null;
    }
    ChallanPaymentStatus paymentStatusEnum =
        ChallanWebviewUtility.mapStringToPaymentStatus(paymentStatus!);

    return switch (paymentStatusEnum) {
      ChallanPaymentStatus.PAYMENT_INITIATED => PaymentStatusModel(
          isSuccess: false,
          text: ChallanAutomationConstants.yourPaymentHasInitiated,
          textColor: color219653,
        ),
      ChallanPaymentStatus.PAYMENT_FAILED => PaymentStatusModel(
          isSuccess: false,
          text: ChallanAutomationConstants.paymentFailedCheckDetails,
          textColor: colorD83D37,
        ),
      ChallanPaymentStatus.PAYMENT_SUCCESS => PaymentStatusModel(
          isSuccess: true,
          text: ChallanAutomationConstants.paymentSuccessfulCheckDetails,
          textColor: color219653,
        ),
      _ => PaymentStatusModel(
          isSuccess: false,
          text: ChallanAutomationConstants.yourPaymentHasInitiated,
          textColor: color219653,
        ),
    };
  }

  static UpiTileConstants getUpiConstantsFromUpiId(String upiId) {
    ChallanUpiProvider upiProvider = ChallanUpiProvider.fromUpiId(upiId);
    String upiProviderImg = getUpiProviderImg(upiProvider);

    return switch (upiProvider) {
      ChallanUpiProvider.cred => UpiTileConstants(
          subtitle: upiId,
          title: cred,
          upiProvider: ChallanUpiProvider.cred,
          imageUrl: upiProviderImg,
        ),
      ChallanUpiProvider.amazonPay => UpiTileConstants(
          subtitle: upiId,
          title: amazon_pay,
          upiProvider: ChallanUpiProvider.amazonPay,
          imageUrl: upiProviderImg,
        ),
      ChallanUpiProvider.phonePe => UpiTileConstants(
          subtitle: upiId,
          title: phone_pe,
          upiProvider: ChallanUpiProvider.phonePe,
          imageUrl: upiProviderImg,
        ),
      ChallanUpiProvider.googlePay => UpiTileConstants(
          subtitle: upiId,
          title: google_pay,
          upiProvider: ChallanUpiProvider.googlePay,
          imageUrl: upiProviderImg,
        ),
      ChallanUpiProvider.paytm => UpiTileConstants(
          subtitle: upiId,
          title: paytm,
          upiProvider: ChallanUpiProvider.paytm,
          imageUrl: upiProviderImg,
        ),
      _ => UpiTileConstants(
          subtitle: upiId,
          upiProvider: ChallanUpiProvider.unknown,
          imageUrl: upiProviderImg,
        ),
    };
  }

  static ChallanPaymentInProgress getPaymentInProgressConstants(String upiID) {
    ChallanUpiProvider upiProvider = ChallanUpiProvider.fromUpiId(upiID);
    String upiProviderImg = getUpiProviderImg(upiProvider);

    return ChallanPaymentInProgress(
      title: upiProvider != ChallanUpiProvider.unknown
          ? ChallanAutomationConstants.buildUpiPaymentMessage(
              ChallanAutomationConstants.providerTitleMap[upiProvider]!)
          : open_generic_upi_app,
      imageUrl: upiProviderImg,
    );
  }

  static String getUpiProviderImg(ChallanUpiProvider upiProvider) {
    return switch (upiProvider) {
      ChallanUpiProvider.paytm => AssetsConstants.paytm_upi,
      ChallanUpiProvider.googlePay => AssetsConstants.google_pay_upi,
      ChallanUpiProvider.phonePe => AssetsConstants.phone_pe_upi,
      ChallanUpiProvider.amazonPay => AssetsConstants.amazon_pay_upi,
      ChallanUpiProvider.cred => AssetsConstants.cred_upi,
      ChallanUpiProvider.unknown => AssetsConstants.upi_generic_img,
    };
  }
}
