import 'dart:convert';

import 'package:utilities/remote_config/remote_config.dart';

enum ChallanSearchSource {
  KA,
  TS,
  MH,
  DL,
  DELHI_TRAFFIC_POLICE,
  PARIVAHAN,
  UNKNOWN;

  static ChallanSearchSource forAutomationFromString(String? value) {
    switch (value) {
      case 'KA':
        return ChallanSearchSource.KA;
      case 'MH':
        return ChallanSearchSource.MH;
      case 'TS':
        return ChallanSearchSource.TS;
      default:
        return ChallanSearchSource.UNKNOWN;
    }
  }

  static ChallanSearchSource fromString(String? value) {
    if (value == null) return ChallanSearchSource.UNKNOWN;

    switch (value.toUpperCase()) {
      case 'KA':
        return ChallanSearchSource.KA;
      case 'MH':
        return ChallanSearchSource.MH;
      case 'DL':
        return ChallanSearchSource.DL;
      case 'DELHI TRAFFIC POLICE':
        return ChallanSearchSource.DELHI_TRAFFIC_POLICE;
      case 'TS':
        return ChallanSearchSource.TS;
      case 'PARIVAHAN':
        return ChallanSearchSource.PARIVAHAN;
      default:
        return ChallanSearchSource.UNKNOWN;
    }
  }
}

enum ChallanUpiProvider {
  paytm('paytm'),
  googlePay('google_pay'),
  phonePe('phone_pe'),
  amazonPay('amazon_pay'),
  cred('cred'),
  unknown('unknown');

  final String key;
  const ChallanUpiProvider(this.key);

  static ChallanUpiProvider fromUpiId(String upiId) {
    final providerSuffixJsonMapping = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.upiProviderSuffixMapping);
    final normalizedUpi = upiId.toLowerCase();

    final upiProviderSuffixMapping = jsonDecode(providerSuffixJsonMapping);

    for (final entry in upiProviderSuffixMapping.entries) {
      if (entry.value.any((substring) => normalizedUpi.contains(substring))) {
        return ChallanUpiProvider.values.firstWhere(
          (provider) => provider.key == entry.key,
          orElse: () => ChallanUpiProvider.unknown,
        );
      }
    }
    return ChallanUpiProvider.unknown;
  }
}
