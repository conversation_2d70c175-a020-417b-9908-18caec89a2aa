class HlEndorsementCopies {
  final Endorsement? endorsement;

  HlEndorsementCopies({
    this.endorsement,
  });

  factory HlEndorsementCopies.fromJson(Map<String, dynamic> json) =>
      HlEndorsementCopies(
        endorsement: json["endorsement"] == null
            ? null
            : Endorsement.fromJson(json["endorsement"]),
      );

  Map<String, dynamic> toJson() => {
        "endorsement": endorsement?.toJson(),
      };
}

class Endorsement {
  final Health? health;
  final Shared? shared;

  Endorsement({
    this.health,
    this.shared,
  });

  factory Endorsement.fromJson(Map<String, dynamic> json) => Endorsement(
        health: json["health"] == null ? null : Health.fromJson(json["health"]),
        shared: json["shared"] == null ? null : Shared.fromJson(json["shared"]),
      );

  Map<String, dynamic> toJson() => {
        "health": health?.toJson(),
        "shared": shared?.toJson(),
      };
}

class Health {
  final HealthRenewal? healthRenewal;
  final PrePolicyEdits? prePolicyEdits;
  final Domain? domain;

  Health({
    this.healthRenewal,
    this.prePolicyEdits,
    this.domain,
  });

  factory Health.fromJson(Map<String, dynamic> json) => Health(
        healthRenewal: json["health_renewal"] == null
            ? null
            : HealthRenewal.fromJson(json["health_renewal"]),
        prePolicyEdits: json["pre_policy_edits"] == null
            ? null
            : PrePolicyEdits.fromJson(json["pre_policy_edits"]),
        domain: json["domain"] == null ? null : Domain.fromJson(json["domain"]),
      );

  Map<String, dynamic> toJson() => {
        "health_renewal": healthRenewal?.toJson(),
        "pre_policy_edits": prePolicyEdits?.toJson(),
        "domain": domain?.toJson(),
      };
}

class HealthRenewal {
  HealthRenewal();

  factory HealthRenewal.fromJson(Map<String, dynamic> json) => HealthRenewal();

  Map<String, dynamic> toJson() => {};
}

class PrePolicyEdits {
  final PrePolicyEditsView? view;

  PrePolicyEdits({
    this.view,
  });

  factory PrePolicyEdits.fromJson(Map<String, dynamic> json) => PrePolicyEdits(
        view: json["view"] == null
            ? null
            : PrePolicyEditsView.fromJson(json["view"]),
      );

  Map<String, dynamic> toJson() => {
        "view": view?.toJson(),
      };
}

class Domain {
  final Models? models;

  Domain({
    this.models,
  });

  factory Domain.fromJson(Map<String, dynamic> json) => Domain(
        models: json["models"] == null ? null : Models.fromJson(json["models"]),
      );

  Map<String, dynamic> toJson() => {
        "models": models?.toJson(),
      };
}

class Models {
  final HealthJmResponse? healthJmResponse;

  Models({
    this.healthJmResponse,
  });

  factory Models.fromJson(Map<String, dynamic> json) => Models(
        healthJmResponse: json["health_jm_response"] == null
            ? null
            : HealthJmResponse.fromJson(json["health_jm_response"]),
      );

  Map<String, dynamic> toJson() => {
        "health_jm_response": healthJmResponse?.toJson(),
      };
}

class HealthJmResponse {
  final String? rejectionReason;
  final String? underReviewReason;

  HealthJmResponse({
    this.rejectionReason,
    this.underReviewReason,
  });

  factory HealthJmResponse.fromJson(Map<String, dynamic> json) =>
      HealthJmResponse(
        rejectionReason: json["rejected_reason"],
        underReviewReason: json["under_review_reason"],
      );

  Map<String, dynamic> toJson() => {
        "rejected_reason": rejectionReason,
        "under_review_reason": underReviewReason,
      };
}

class PrePolicyEditsView {
  final PurpleWidgets? widgets;
  final Screens? screens;
  final Bottomsheets? bottomsheets;

  PrePolicyEditsView({
    this.widgets,
    this.screens,
    this.bottomsheets,
  });

  factory PrePolicyEditsView.fromJson(Map<String, dynamic> json) =>
      PrePolicyEditsView(
        widgets: json["widgets"] == null
            ? null
            : PurpleWidgets.fromJson(json["widgets"]),
        screens:
            json["screens"] == null ? null : Screens.fromJson(json["screens"]),
        bottomsheets: json["bottomsheets"] == null
            ? null
            : Bottomsheets.fromJson(json["bottomsheets"]),
      );

  Map<String, dynamic> toJson() => {
        "widgets": widgets?.toJson(),
        "screens": screens?.toJson(),
        "bottomsheets": bottomsheets?.toJson(),
      };
}

class Bottomsheets {
  final PpeChildNotCoveredSheet? ppeChildNotCoveredSheet;
  final PpeDraftChangesSheet? ppeDraftChangesSheet;
  final PpeEvaluationRequiredSheet? ppeEvaluationRequiredSheet;
  final PpeNonEditableFieldsSheet? ppeNonEditableFieldsSheet;
  final PpePolicyStartDateSheet? ppePolicyStartDateSheet;
  final PpeRemoveMemberSheet? ppeRemoveMemberSheet;
  final PpeReviewChangesSheet? ppeReviewChangesSheet;
  final PpeDeleteDraftConfirmationSheet? ppeDeleteDraftConfirmationSheet;

  Bottomsheets({
    this.ppeChildNotCoveredSheet,
    this.ppeDraftChangesSheet,
    this.ppeEvaluationRequiredSheet,
    this.ppeNonEditableFieldsSheet,
    this.ppePolicyStartDateSheet,
    this.ppeRemoveMemberSheet,
    this.ppeReviewChangesSheet,
    this.ppeDeleteDraftConfirmationSheet,
  });

  factory Bottomsheets.fromJson(Map<String, dynamic> json) => Bottomsheets(
        ppeChildNotCoveredSheet: json["ppe_child_not_covered_sheet"] == null
            ? null
            : PpeChildNotCoveredSheet.fromJson(
                json["ppe_child_not_covered_sheet"]),
        ppeDraftChangesSheet: json["ppe_draft_changes_sheet"] == null
            ? null
            : PpeDraftChangesSheet.fromJson(json["ppe_draft_changes_sheet"]),
        ppeEvaluationRequiredSheet:
            json["ppe_evaluation_required_sheet"] == null
                ? null
                : PpeEvaluationRequiredSheet.fromJson(
                    json["ppe_evaluation_required_sheet"]),
        ppeNonEditableFieldsSheet: json["ppe_non_editable_fields_sheet"] == null
            ? null
            : PpeNonEditableFieldsSheet.fromJson(
                json["ppe_non_editable_fields_sheet"]),
        ppePolicyStartDateSheet: json["ppe_policy_start_date_sheet"] == null
            ? null
            : PpePolicyStartDateSheet.fromJson(
                json["ppe_policy_start_date_sheet"]),
        ppeRemoveMemberSheet: json["ppe_remove_member_sheet"] == null
            ? null
            : PpeRemoveMemberSheet.fromJson(json["ppe_remove_member_sheet"]),
        ppeReviewChangesSheet: json["ppe_review_changes_sheet"] == null
            ? null
            : PpeReviewChangesSheet.fromJson(json["ppe_review_changes_sheet"]),
        ppeDeleteDraftConfirmationSheet:
            json["ppe_delete_draft_confirmation_sheet"] == null
                ? null
                : PpeDeleteDraftConfirmationSheet.fromJson(
                    json["ppe_delete_draft_confirmation_sheet"]),
      );

  Map<String, dynamic> toJson() => {
        "ppe_child_not_covered_sheet": ppeChildNotCoveredSheet?.toJson(),
        "ppe_draft_changes_sheet": ppeDraftChangesSheet?.toJson(),
        "ppe_evaluation_required_sheet": ppeEvaluationRequiredSheet?.toJson(),
        "ppe_non_editable_fields_sheet": ppeNonEditableFieldsSheet?.toJson(),
        "ppe_policy_start_date_sheet": ppePolicyStartDateSheet?.toJson(),
        "ppe_remove_member_sheet": ppeRemoveMemberSheet?.toJson(),
        "ppe_review_changes_sheet": ppeReviewChangesSheet?.toJson(),
        "ppe_delete_draft_confirmation_sheet":
            ppeDeleteDraftConfirmationSheet?.toJson(),
      };
}

class PpeChildNotCoveredSheet {
  final String? cannotCoverBabies;
  final String? onceTurned;
  final String? callUs;

  PpeChildNotCoveredSheet({
    this.cannotCoverBabies,
    this.onceTurned,
    this.callUs,
  });

  factory PpeChildNotCoveredSheet.fromJson(Map<String, dynamic> json) =>
      PpeChildNotCoveredSheet(
        cannotCoverBabies: json["cannot_cover_babies"],
        onceTurned: json["once_turned"],
        callUs: json["call_us"],
      );

  Map<String, dynamic> toJson() => {
        "cannot_cover_babies": cannotCoverBabies,
        "once_turned": onceTurned,
        "call_us": callUs,
      };
}

class PpeDraftChangesSheet {
  final String? ackoWillRefund;
  final String? premiumToBePaid;
  final String? reviewEditsPay;
  final String? proceedToPay;
  final String? deleteThisDraft;
  final String? reviewAndSubmit;
  final String? reviewAndUploadDocuments;
  final String? reviewAndUpload;
  final String? continueEditing;
  final String? draftDays;
  final DraftState? draftState;

  PpeDraftChangesSheet({
    this.ackoWillRefund,
    this.premiumToBePaid,
    this.reviewEditsPay,
    this.proceedToPay,
    this.deleteThisDraft,
    this.reviewAndSubmit,
    this.reviewAndUploadDocuments,
    this.reviewAndUpload,
    this.continueEditing,
    this.draftState,
    this.draftDays
  });

  factory PpeDraftChangesSheet.fromJson(Map<String, dynamic> json) =>
      PpeDraftChangesSheet(
        ackoWillRefund: json["acko_will_refund"],
        premiumToBePaid: json["premium_to_be_paid"],
        reviewEditsPay: json["review_edits_pay"],
        proceedToPay: json["proceed_to_pay"],
        deleteThisDraft: json["delete_this_draft"],
        reviewAndSubmit: json["review_and_submit"],
        reviewAndUploadDocuments: json["review_and_upload_documents"],
        reviewAndUpload: json["review_and_upload"],
        continueEditing: json["continue_editing"],
        draftDays: json["draft_days"],
        draftState: json["draft_state"] == null
            ? null
            : DraftState.fromJson(json["draft_state"]),
      );

  Map<String, dynamic> toJson() => {
        "acko_will_refund": ackoWillRefund,
        "premium_to_be_paid": premiumToBePaid,
        "review_edits_pay": reviewEditsPay,
        "proceed_to_pay": proceedToPay,
        "delete_this_draft": deleteThisDraft,
        "review_and_submit": reviewAndSubmit,
        "review_and_upload_documents": reviewAndUploadDocuments,
        "review_and_upload": reviewAndUpload,
        "continue_editing": continueEditing,
        "draft_days": draftDays,
        "draft_state": draftState?.toJson(),
      };
}

class DraftState {
  final UnderReview? underReview;
  final DocumentRequired? paymentAndDocRequired;
  final DocumentRequired? refundAndDocRequired;
  final DocumentRequired? paymentRequired;
  final DocumentRequired? refundRequired;
  final DocumentRequired? documentRequired;
  final PpeRemoveMemberSheet? draftStateDefault;
  final DocumentRequired? none;
  final DocumentRequired? basbaPayment;
  final DocumentRequired? basbaRefund;
  final DocumentRequired? basbaRefundAndDoc;

  DraftState(
      {this.underReview,
      this.paymentAndDocRequired,
      this.refundAndDocRequired,
      this.paymentRequired,
      this.refundRequired,
      this.documentRequired,
      this.draftStateDefault,
      this.none,
      this.basbaPayment,
      this.basbaRefund,
      this.basbaRefundAndDoc});

  factory DraftState.fromJson(Map<String, dynamic> json) => DraftState(
        underReview: json["under_review"] == null
            ? null
            : UnderReview.fromJson(json["under_review"]),
        paymentAndDocRequired: json["payment_and_doc_required"] == null
            ? null
            : DocumentRequired.fromJson(json["payment_and_doc_required"]),
        refundAndDocRequired: json["refund_and_doc_required"] == null
            ? null
            : DocumentRequired.fromJson(json["refund_and_doc_required"]),
        paymentRequired: json["payment_required"] == null
            ? null
            : DocumentRequired.fromJson(json["payment_required"]),
        refundRequired: json["refund_required"] == null
            ? null
            : DocumentRequired.fromJson(json["refund_required"]),
        documentRequired: json["document_required"] == null
            ? null
            : DocumentRequired.fromJson(json["document_required"]),
        none: json["none"] == null
            ? null
            : DocumentRequired.fromJson(json["none"]),
        draftStateDefault: json["default"] == null
            ? null
            : PpeRemoveMemberSheet.fromJson(json["default"]),
        basbaPayment: json["basba_payment"] == null
            ? null
            : DocumentRequired.fromJson(json["basba_payment"]),
        basbaRefund: json["basba_refund"] == null
            ? null
            : DocumentRequired.fromJson(json["basba_refund"]),
        basbaRefundAndDoc: json["basba_refund_and_doc"] == null
            ? null
            : DocumentRequired.fromJson(json["basba_refund_and_doc"]),
      );

  Map<String, dynamic> toJson() => {
        "under_review": underReview?.toJson(),
        "payment_and_doc_required": paymentAndDocRequired?.toJson(),
        "refund_and_doc_required": refundAndDocRequired?.toJson(),
        "payment_required": paymentRequired?.toJson(),
        "refund_required": refundRequired?.toJson(),
        "document_required": documentRequired?.toJson(),
        "none": none?.toJson(),
        "default": draftStateDefault?.toJson(),
        "basba_payment": basbaPayment?.toJson(),
        "basba_refund": basbaRefund?.toJson(),
        "basba_refund_and_doc": basbaRefundAndDoc?.toJson(),
      };
}

class DocumentRequired {
  final String? subtitle;
  final String? draftExpirationSubtitle; /// dynamically uses expiration days here via sprintf

  DocumentRequired({this.subtitle, this.draftExpirationSubtitle});

  factory DocumentRequired.fromJson(Map<String, dynamic> json) =>
      DocumentRequired(
        subtitle: json["subtitle"],
        draftExpirationSubtitle: json["draft_expiration_subtitle"],
      );

  Map<String, dynamic> toJson() => {
        "subtitle": subtitle,
        "draft_expiration_subtitle": draftExpirationSubtitle,
      };
}

class PpeRemoveMemberSheet {
  final String? title;
  final String? iconUrl;
  final String? basbaTitle;

  PpeRemoveMemberSheet({
    this.title,
    this.iconUrl,
    this.basbaTitle
  });

  factory PpeRemoveMemberSheet.fromJson(Map<String, dynamic> json) =>
      PpeRemoveMemberSheet(
        title: json["title"],
        iconUrl: json["icon_url"],
        basbaTitle: json["basba_title"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "icon_url": iconUrl,
        "basba_title": basbaTitle,
      };
}

class UnderReview {
  final String? title;
  final String? subtitle;
  final String? iconUrl;

  UnderReview({
    this.title,
    this.subtitle,
    this.iconUrl,
  });

  factory UnderReview.fromJson(Map<String, dynamic> json) => UnderReview(
        title: json["title"],
        subtitle: json["subtitle"],
        iconUrl: json["icon_url"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "subtitle": subtitle,
        "icon_url": iconUrl,
      };
}

class PpeEvaluationRequiredSheet {
  final String? healthEvaluationRequired;
  final String? currentPriceBasis;
  final String? whatIsHealthEvaluation;

  PpeEvaluationRequiredSheet({
    this.healthEvaluationRequired,
    this.currentPriceBasis,
    this.whatIsHealthEvaluation,
  });

  factory PpeEvaluationRequiredSheet.fromJson(Map<String, dynamic> json) =>
      PpeEvaluationRequiredSheet(
        healthEvaluationRequired: json["health_evaluation_required"],
        currentPriceBasis: json["current_price_basis"],
        whatIsHealthEvaluation: json["what_is_health_evaluation"],
      );

  Map<String, dynamic> toJson() => {
        "health_evaluation_required": healthEvaluationRequired,
        "current_price_basis": currentPriceBasis,
        "what_is_health_evaluation": whatIsHealthEvaluation,
      };
}

class PpeNonEditableFieldsSheet {
  final String? title;
  final String? subtitle;
  final String? callUs;

  PpeNonEditableFieldsSheet({
    this.title,
    this.subtitle,
    this.callUs,
  });

  factory PpeNonEditableFieldsSheet.fromJson(Map<String, dynamic> json) =>
      PpeNonEditableFieldsSheet(
        title: json["title"],
        subtitle: json["subtitle"],
        callUs: json["call_us"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "subtitle": subtitle,
        "call_us": callUs,
      };
}

class PpePolicyStartDateSheet {
  final String? title;
  final EditPolicyType? editPolicyType;

  PpePolicyStartDateSheet({
    this.title,
    this.editPolicyType,
  });

  factory PpePolicyStartDateSheet.fromJson(Map<String, dynamic> json) =>
      PpePolicyStartDateSheet(
        title: json["title"],
        editPolicyType: json["edit_policy_type"] == null
            ? null
            : EditPolicyType.fromJson(json["edit_policy_type"]),
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "edit_policy_type": editPolicyType?.toJson(),
      };
}

class EditPolicyType {
  final DocumentRequired? payment;
  final DocumentRequired? porting;
  final DocumentRequired? renewal;

  EditPolicyType({
    this.payment,
    this.porting,
    this.renewal,
  });

  factory EditPolicyType.fromJson(Map<String, dynamic> json) => EditPolicyType(
        payment: json["payment"] == null
            ? null
            : DocumentRequired.fromJson(json["payment"]),
        porting: json["porting"] == null
            ? null
            : DocumentRequired.fromJson(json["porting"]),
        renewal: json["renewal"] == null
            ? null
            : DocumentRequired.fromJson(json["renewal"]),
      );

  Map<String, dynamic> toJson() => {
        "payment": payment?.toJson(),
        "porting": porting?.toJson(),
        "renewal": renewal?.toJson(),
      };
}

class PpeReviewChangesSheet {
  final String? title;
  final String? totalEdits;
  final String? refundAmount;
  final String? premiumToBePaid;
  final PpeOverviewScreen? policyChangesTitles;

  PpeReviewChangesSheet({
    this.title,
    this.totalEdits,
    this.refundAmount,
    this.premiumToBePaid,
    this.policyChangesTitles,
  });

  factory PpeReviewChangesSheet.fromJson(Map<String, dynamic> json) =>
      PpeReviewChangesSheet(
        title: json["title"],
        totalEdits: json["total_edits"],
        refundAmount: json["refund_amount"],
        premiumToBePaid: json["premium_to_be_paid"],
        policyChangesTitles: json["policy_changes_titles"] == null
            ? null
            : PpeOverviewScreen.fromJson(json["policy_changes_titles"]),
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "total_edits": totalEdits,
        "refund_amount": refundAmount,
        "premium_to_be_paid": premiumToBePaid,
        "policy_changes_titles": policyChangesTitles?.toJson(),
      };
}

class PpeDeleteDraftConfirmationSheet {
  final String? title;
  final String? subtitle;
  final String? primaryCtaText;
  final String? secondaryCtaText;

  PpeDeleteDraftConfirmationSheet({
    this.title,
    this.subtitle,
    this.primaryCtaText,
    this.secondaryCtaText,
  });

  factory PpeDeleteDraftConfirmationSheet.fromJson(Map<String, dynamic> json) =>
      PpeDeleteDraftConfirmationSheet(
        title: json["title"],
        subtitle: json["subtitle"],
        primaryCtaText: json["primary_cta_text"],
        secondaryCtaText: json["secondary_cta_text"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "subtitle": subtitle,
        "primary_cta_text": primaryCtaText,
        "secondary_cta_text": secondaryCtaText,
      };
}

class PpeOverviewScreen {
  final String? portingDetails;
  final String? policyDetails;
  final String? memberDetails;
  final String? memberAdditionRemoval;

  PpeOverviewScreen({
    this.portingDetails,
    this.policyDetails,
    this.memberDetails,
    this.memberAdditionRemoval,
  });

  factory PpeOverviewScreen.fromJson(Map<String, dynamic> json) =>
      PpeOverviewScreen(
        portingDetails: json["porting_details"],
        policyDetails: json["policy_details"],
        memberDetails: json["member_details"],
        memberAdditionRemoval: json["member_addition_removal"],
      );

  Map<String, dynamic> toJson() => {
        "porting_details": portingDetails,
        "policy_details": policyDetails,
        "member_details": memberDetails,
        "member_addition_removal": memberAdditionRemoval,
      };
}

class Screens {
  final PpeRemoveMemberSheet? ppeChangesSubmittedScreen;
  final PpeCheckoutScreen? ppeCheckoutScreen;
  final PpeEditScreen? ppeEditScreen;
  final PpeEditTrackingScreen? ppeEditTrackingScreen;
  final PpeOverviewScreen? ppeOverviewScreen;
  final PpeReviewChangesScreen? ppeReviewChangesScreen;
  final PpeMandateCancellationScreen? ppeMandateCancellationScreen;

  Screens(
      {this.ppeChangesSubmittedScreen,
      this.ppeCheckoutScreen,
      this.ppeEditScreen,
      this.ppeEditTrackingScreen,
      this.ppeOverviewScreen,
      this.ppeReviewChangesScreen,
      this.ppeMandateCancellationScreen});

  factory Screens.fromJson(Map<String, dynamic> json) => Screens(
        ppeChangesSubmittedScreen: json["ppe_changes_submitted_screen"] == null
            ? null
            : PpeRemoveMemberSheet.fromJson(
                json["ppe_changes_submitted_screen"]),
        ppeCheckoutScreen: json["ppe_checkout_screen"] == null
            ? null
            : PpeCheckoutScreen.fromJson(json["ppe_checkout_screen"]),
        ppeEditScreen: json["ppe_edit_screen"] == null
            ? null
            : PpeEditScreen.fromJson(json["ppe_edit_screen"]),
        ppeEditTrackingScreen: json["ppe_edit_tracking_screen"] == null
            ? null
            : PpeEditTrackingScreen.fromJson(json["ppe_edit_tracking_screen"]),
        ppeOverviewScreen: json["ppe_overview_screen"] == null
            ? null
            : PpeOverviewScreen.fromJson(json["ppe_overview_screen"]),
        ppeReviewChangesScreen: json["ppe_review_changes_screen"] == null
            ? null
            : PpeReviewChangesScreen.fromJson(
                json["ppe_review_changes_screen"]),
        ppeMandateCancellationScreen:
            json["ppe_mandate_cancellation_screen"] == null
                ? null
                : PpeMandateCancellationScreen.fromJson(
                    json["ppe_mandate_cancellation_screen"]),
      );

  Map<String, dynamic> toJson() => {
        "ppe_changes_submitted_screen": ppeChangesSubmittedScreen?.toJson(),
        "ppe_checkout_screen": ppeCheckoutScreen?.toJson(),
        "ppe_edit_screen": ppeEditScreen?.toJson(),
        "ppe_edit_tracking_screen": ppeEditTrackingScreen?.toJson(),
        "ppe_overview_screen": ppeOverviewScreen?.toJson(),
        "ppe_review_changes_screen": ppeReviewChangesScreen?.toJson(),
      };
}

class PpeCheckoutScreen {
  final String? title;
  final String? subtitle;
  final PremiumComparision? premiumComparision;
  final PremiumDetails? premiumDetails;

  PpeCheckoutScreen({
    this.title,
    this.subtitle,
    this.premiumComparision,
    this.premiumDetails,
  });

  factory PpeCheckoutScreen.fromJson(Map<String, dynamic> json) =>
      PpeCheckoutScreen(
        title: json["title"],
        subtitle: json["subtitle"],
        premiumComparision: json["premium_comparision"] == null
            ? null
            : PremiumComparision.fromJson(json["premium_comparision"]),
        premiumDetails: json["premium_details"] == null
            ? null
            : PremiumDetails.fromJson(json["premium_details"]),
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "subtitle": subtitle,
        "premium_comparision": premiumComparision?.toJson(),
        "premium_details": premiumDetails?.toJson(),
      };
}

class PremiumComparision {
  final String? oldPremium;
  final String? newPremium;

  PremiumComparision({
    this.oldPremium,
    this.newPremium,
  });

  factory PremiumComparision.fromJson(Map<String, dynamic> json) =>
      PremiumComparision(
        oldPremium: json["old_premium"],
        newPremium: json["new_premium"],
      );

  Map<String, dynamic> toJson() => {
        "old_premium": oldPremium,
        "new_premium": newPremium,
      };
}

class PremiumDetails {
  final String? premiumDue;
  final String? premiumDetail;
  final String? totalPremiumDue;
  final String? alreadyPaid;
  final String? ackoWillRefund;
  final String? youNeedToPay;

  PremiumDetails({
    this.premiumDue,
    this.premiumDetail,
    this.totalPremiumDue,
    this.alreadyPaid,
    this.ackoWillRefund,
    this.youNeedToPay,
  });

  factory PremiumDetails.fromJson(Map<String, dynamic> json) => PremiumDetails(
        premiumDue: json["premium_due"],
        premiumDetail: json["premium_detail"],
        totalPremiumDue: json["total_premium_due"],
        alreadyPaid: json["already_paid"],
        ackoWillRefund: json["acko_will_refund"],
        youNeedToPay: json["you_need_to_pay"],
      );

  Map<String, dynamic> toJson() => {
        "premium_due": premiumDue,
        "premium_detail": premiumDetail,
        "total_premium_due": totalPremiumDue,
        "already_paid": alreadyPaid,
        "acko_will_refund": ackoWillRefund,
        "you_need_to_pay": youNeedToPay,
      };
}

class PpeEditScreen {
  final String? title;
  final String? description;
  final String? portingDetails;
  final String? policyDetails;
  final String? memberDetails;
  final String? addMembers;
  final int? portingExpiryDaysLimit;

  PpeEditScreen({
    this.title,
    this.description,
    this.portingDetails,
    this.policyDetails,
    this.memberDetails,
    this.addMembers,
    this.portingExpiryDaysLimit
  });

  factory PpeEditScreen.fromJson(Map<String, dynamic> json) => PpeEditScreen(
        title: json["title"],
        description: json["description"],
        portingDetails: json["porting_details"],
        policyDetails: json["policy_details"],
        memberDetails: json["member_details"],
        addMembers: json["add_members"],
        portingExpiryDaysLimit: json["porting_expiry_days_limit"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "description": description,
        "porting_details": portingDetails,
        "policy_details": policyDetails,
        "member_details": memberDetails,
        "add_members": addMembers,
        "porting_expiry_days_limit": portingExpiryDaysLimit,
      };
}

class PpeEditTrackingScreen {
  final String? headerText;

  PpeEditTrackingScreen({
    this.headerText,
  });

  factory PpeEditTrackingScreen.fromJson(Map<String, dynamic> json) =>
      PpeEditTrackingScreen(
        headerText: json["header_text"],
      );

  Map<String, dynamic> toJson() => {
        "header_text": headerText,
      };
}

class PpeReviewChangesScreen {
  final String? uploadDocAndCheckbox;
  final String? uploadDoc;
  final String? reviewAndUpload;
  final String? review;
  final String? totalEdits;
  final String? portingDetails;
  final String? policyDetails;
  final String? memberDetails;
  final String? memberAdditionRemoval;

  PpeReviewChangesScreen({
    this.uploadDocAndCheckbox,
    this.uploadDoc,
    this.reviewAndUpload,
    this.review,
    this.totalEdits,
    this.portingDetails,
    this.policyDetails,
    this.memberDetails,
    this.memberAdditionRemoval,
  });

  factory PpeReviewChangesScreen.fromJson(Map<String, dynamic> json) =>
      PpeReviewChangesScreen(
        uploadDocAndCheckbox: json["upload_doc_and_checkbox"],
        uploadDoc: json["upload_doc"],
        reviewAndUpload: json["review_and_upload"],
        review: json["review"],
        totalEdits: json["total_edits"],
        portingDetails: json["porting_details"],
        policyDetails: json["policy_details"],
        memberDetails: json["member_details"],
        memberAdditionRemoval: json["member_addition_removal"],
      );

  Map<String, dynamic> toJson() => {
        "upload_doc_and_checkbox": uploadDocAndCheckbox,
        "upload_doc": uploadDoc,
        "review_and_upload": reviewAndUpload,
        "review": review,
        "total_edits": totalEdits,
        "porting_details": portingDetails,
        "policy_details": policyDetails,
        "member_details": memberDetails,
        "member_addition_removal": memberAdditionRemoval,
      };
}

class PpeMandateCancellationScreen {
  String? title;
  String? subtitle;
  String? contactNumber;
  String? mailId;

  PpeMandateCancellationScreen(
      {this.title, this.subtitle, this.contactNumber, this.mailId});

  factory PpeMandateCancellationScreen.fromJson(Map<String, dynamic> json) =>
      PpeMandateCancellationScreen(
          title: json["title"],
          subtitle: json["subtitle"],
          contactNumber: json["contact_number"],
          mailId: json["mail_id"]);

  Map<String, dynamic> toJson() => {
        "title": title,
        "subtitle": subtitle,
        "contact_number": contactNumber,
        "mail_id": mailId
      };
}

class PurpleWidgets {
  final CallUsNowPpe? callUsNowPpe;
  final EditOverviewHeader? editOverviewHeader;
  final EditTrackingHeaderWidget? editTrackingHeaderWidget;
  final PolicyOverviewDetails? policyOverviewDetails;

  PurpleWidgets({
    this.callUsNowPpe,
    this.editOverviewHeader,
    this.editTrackingHeaderWidget,
    this.policyOverviewDetails,
  });

  factory PurpleWidgets.fromJson(Map<String, dynamic> json) => PurpleWidgets(
        callUsNowPpe: json["call_us_now_ppe"] == null
            ? null
            : CallUsNowPpe.fromJson(json["call_us_now_ppe"]),
        editOverviewHeader: json["edit_overview_header"] == null
            ? null
            : EditOverviewHeader.fromJson(json["edit_overview_header"]),
        editTrackingHeaderWidget: json["edit_tracking_header_widget"] == null
            ? null
            : EditTrackingHeaderWidget.fromJson(
                json["edit_tracking_header_widget"]),
        policyOverviewDetails: json["policy_overview_details"] == null
            ? null
            : PolicyOverviewDetails.fromJson(json["policy_overview_details"]),
      );

  Map<String, dynamic> toJson() => {
        "call_us_now_ppe": callUsNowPpe?.toJson(),
        "edit_overview_header": editOverviewHeader?.toJson(),
        "edit_tracking_header_widget": editTrackingHeaderWidget?.toJson(),
        "policy_overview_details": policyOverviewDetails?.toJson(),
      };
}

class CallUsNowPpe {
  final String? needToShare;
  final String? talkToUsTitle;
  final String? callUsNow;
  final bool? show;

  CallUsNowPpe({
    this.needToShare,
    this.talkToUsTitle,
    this.callUsNow,
    this.show,
  });

  factory CallUsNowPpe.fromJson(Map<String, dynamic> json) => CallUsNowPpe(
        needToShare: json["need_to_share"],
        talkToUsTitle: json["talk_to_us_title"],
        callUsNow: json["call_us_now"],
        show: json["show"],
      );

  Map<String, dynamic> toJson() => {
        "need_to_share": needToShare,
        "talk_to_us_title": talkToUsTitle,
        "call_us_now": callUsNow,
        "show": show,
      };
}

class EditOverviewHeader {
  final String? title;
  final String? policyStartDate;
  final Map<String, NotificationCard>? notificationCards;

  EditOverviewHeader({
    this.title,
    this.policyStartDate,
    this.notificationCards,
  });

  factory EditOverviewHeader.fromJson(Map<String, dynamic> json) =>
      EditOverviewHeader(
        title: json["title"],
        policyStartDate: json["policy_start_date"],
        notificationCards: json["notification_cards"] == null
            ? null
            : Map.from(json["notification_cards"]).map(
                (k, v) => MapEntry(k, NotificationCard.fromJson(v)),
              ),
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "policy_start_date": policyStartDate,
        "notification_cards": notificationCards?.map(
          (k, v) => MapEntry(k, v.toJson()),
        ),
      };
}

class NotificationCard {
  final String? title;
  final String? subtitle;
  final String? ctaText;
  final String? id;

  NotificationCard({
    this.title,
    this.subtitle,
    this.ctaText,
    this.id,
  });

  factory NotificationCard.fromJson(Map<String, dynamic> json) =>
      NotificationCard(
        title: json["title"],
        subtitle: json["subtitle"],
        ctaText: json["cta_text"],
        id: json["id"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "subtitle": subtitle,
        "cta_text": ctaText,
        "id": id,
      };
}

class EditTrackingHeaderWidget {
  final TrackingState? trackingState;

  EditTrackingHeaderWidget({
    this.trackingState,
  });

  factory EditTrackingHeaderWidget.fromJson(Map<String, dynamic> json) =>
      EditTrackingHeaderWidget(
        trackingState: json["tracking_state"] == null
            ? null
            : TrackingState.fromJson(json["tracking_state"]),
      );

  Map<String, dynamic> toJson() => {
        "tracking_state": trackingState?.toJson(),
      };
}

class TrackingState {
  final PpeEditTrackingScreen? changesTracking;
  final PpeEditTrackingScreen? changesSubmitted;

  TrackingState({
    this.changesTracking,
    this.changesSubmitted,
  });

  factory TrackingState.fromJson(Map<String, dynamic> json) => TrackingState(
        changesTracking: json["changes_tracking"] == null
            ? null
            : PpeEditTrackingScreen.fromJson(json["changes_tracking"]),
        changesSubmitted: json["changes_submitted"] == null
            ? null
            : PpeEditTrackingScreen.fromJson(json["changes_submitted"]),
      );

  Map<String, dynamic> toJson() => {
        "changes_tracking": changesTracking?.toJson(),
        "changes_submitted": changesSubmitted?.toJson(),
      };
}

class PolicyOverviewDetails {
  final PortingDetailsView? portingDetailsView;
  final PolicyDetailsView? policyDetailsView;
  final MemberDetailsView? memberDetailsView;
  final PolicyOverviewExpansionTile? policyOverviewExpansionTile;

  PolicyOverviewDetails({
    this.portingDetailsView,
    this.policyDetailsView,
    this.memberDetailsView,
    this.policyOverviewExpansionTile,
  });

  factory PolicyOverviewDetails.fromJson(Map<String, dynamic> json) =>
      PolicyOverviewDetails(
        portingDetailsView: json["porting_details_view"] == null
            ? null
            : PortingDetailsView.fromJson(json["porting_details_view"]),
        policyDetailsView: json["policy_details_view"] == null
            ? null
            : PolicyDetailsView.fromJson(json["policy_details_view"]),
        memberDetailsView: json["member_details_view"] == null
            ? null
            : MemberDetailsView.fromJson(json["member_details_view"]),
        policyOverviewExpansionTile:
            json["policy_overview_expansion_tile"] == null
                ? null
                : PolicyOverviewExpansionTile.fromJson(
                    json["policy_overview_expansion_tile"]),
      );

  Map<String, dynamic> toJson() => {
        "porting_details_view": portingDetailsView?.toJson(),
        "policy_details_view": policyDetailsView?.toJson(),
        "member_details_view": memberDetailsView?.toJson(),
        "policy_overview_expansion_tile": policyOverviewExpansionTile?.toJson(),
      };
}

class MemberDetailsView {
  final SingleMemberView? singleMemberView;

  MemberDetailsView({
    this.singleMemberView,
  });

  factory MemberDetailsView.fromJson(Map<String, dynamic> json) =>
      MemberDetailsView(
        singleMemberView: json["single_member_view"] == null
            ? null
            : SingleMemberView.fromJson(json["single_member_view"]),
      );

  Map<String, dynamic> toJson() => {
        "single_member_view": singleMemberView?.toJson(),
      };
}

class SingleMemberView {
  final String? proposerNote;
  final String? relationship;
  final String? dob;
  final String? gender;
  final String? height;
  final String? weight;
  final String? mobileNumber;
  final String? email;
  final String? pinCode;

  SingleMemberView({
    this.proposerNote,
    this.relationship,
    this.dob,
    this.gender,
    this.height,
    this.weight,
    this.mobileNumber,
    this.email,
    this.pinCode,
  });

  factory SingleMemberView.fromJson(Map<String, dynamic> json) =>
      SingleMemberView(
        proposerNote: json["proposer_note"],
        relationship: json["relationship"],
        dob: json["dob"],
        gender: json["gender"],
        height: json["height"],
        weight: json["weight"],
        mobileNumber: json["mobile_number"],
        email: json["email"],
        pinCode: json["pin_code"],
      );

  Map<String, dynamic> toJson() => {
        "proposer_note": proposerNote,
        "relationship": relationship,
        "dob": dob,
        "gender": gender,
        "height": height,
        "weight": weight,
        "mobile_number": mobileNumber,
        "email": email,
        "pin_code": pinCode,
      };
}

class PolicyDetailsView {
  final String? yourPlan;
  final String? sumInsured;
  final String? deductible;

  PolicyDetailsView({
    this.yourPlan,
    this.sumInsured,
    this.deductible,
  });

  factory PolicyDetailsView.fromJson(Map<String, dynamic> json) =>
      PolicyDetailsView(
        yourPlan: json["your_plan"],
        sumInsured: json["sum_insured"],
        deductible: json["deductible"],
      );

  Map<String, dynamic> toJson() => {
        "your_plan": yourPlan,
        "sum_insured": sumInsured,
        "deductible": deductible,
      };
}

class PolicyOverviewExpansionTile {
  final String? edit;

  PolicyOverviewExpansionTile({
    this.edit,
  });

  factory PolicyOverviewExpansionTile.fromJson(Map<String, dynamic> json) =>
      PolicyOverviewExpansionTile(
        edit: json["edit"],
      );

  Map<String, dynamic> toJson() => {
        "edit": edit,
      };
}

class PortingDetailsView {
  final String? previousPolicyEndDate;

  PortingDetailsView({
    this.previousPolicyEndDate,
  });

  factory PortingDetailsView.fromJson(Map<String, dynamic> json) =>
      PortingDetailsView(
        previousPolicyEndDate: json["previous_policy_end_date"],
      );

  Map<String, dynamic> toJson() => {
        "previous_policy_end_date": previousPolicyEndDate,
      };
}

class Shared {
  final SharedView? view;

  Shared({
    this.view,
  });

  factory Shared.fromJson(Map<String, dynamic> json) => Shared(
        view: json["view"] == null ? null : SharedView.fromJson(json["view"]),
      );

  Map<String, dynamic> toJson() => {
        "view": view?.toJson(),
      };
}

class SharedView {
  final AddMember? addMember;
  final Forms? forms;
  final FluffyWidgets? widgets;

  SharedView({
    this.addMember,
    this.forms,
    this.widgets,
  });

  factory SharedView.fromJson(Map<String, dynamic> json) => SharedView(
        addMember: json["add_member"] == null
            ? null
            : AddMember.fromJson(json["add_member"]),
        forms: json["forms"] == null ? null : Forms.fromJson(json["forms"]),
        widgets: json["widgets"] == null
            ? null
            : FluffyWidgets.fromJson(json["widgets"]),
      );

  Map<String, dynamic> toJson() => {
        "add_member": addMember?.toJson(),
        "forms": forms?.toJson(),
        "widgets": widgets?.toJson(),
      };
}

class AddMember {
  final AddMemberScreen? addMemberScreen;

  AddMember({
    this.addMemberScreen,
  });

  factory AddMember.fromJson(Map<String, dynamic> json) => AddMember(
        addMemberScreen: json["add_member_screen"] == null
            ? null
            : AddMemberScreen.fromJson(json["add_member_screen"]),
      );

  Map<String, dynamic> toJson() => {
        "add_member_screen": addMemberScreen?.toJson(),
      };
}

class AddMemberScreen {
  final String? title;
  final String? cannotCoverBabies;
  final DobInputClass? nameInput;
  final DobInputClass? dobInput;
  final GenderInputClass? relationshipInput;
  final GenderInputClass? genderInput;
  final DobInputClass? heightInput;
  final DobInputClass? weightInput;

  AddMemberScreen({
    this.title,
    this.cannotCoverBabies,
    this.nameInput,
    this.dobInput,
    this.relationshipInput,
    this.genderInput,
    this.heightInput,
    this.weightInput,
  });

  factory AddMemberScreen.fromJson(Map<String, dynamic> json) =>
      AddMemberScreen(
        title: json["title"],
        cannotCoverBabies: json["cannot_cover_babies"],
        nameInput: json["name_input"] == null
            ? null
            : DobInputClass.fromJson(json["name_input"]),
        dobInput: json["dob_input"] == null
            ? null
            : DobInputClass.fromJson(json["dob_input"]),
        relationshipInput: json["relationship_input"] == null
            ? null
            : GenderInputClass.fromJson(json["relationship_input"]),
        genderInput: json["gender_input"] == null
            ? null
            : GenderInputClass.fromJson(json["gender_input"]),
        heightInput: json["height_input"] == null
            ? null
            : DobInputClass.fromJson(json["height_input"]),
        weightInput: json["weight_input"] == null
            ? null
            : DobInputClass.fromJson(json["weight_input"]),
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "cannot_cover_babies": cannotCoverBabies,
        "name_input": nameInput?.toJson(),
        "dob_input": dobInput?.toJson(),
        "relationship_input": relationshipInput?.toJson(),
        "gender_input": genderInput?.toJson(),
        "height_input": heightInput?.toJson(),
        "weight_input": weightInput?.toJson(),
      };
}

class DobInputClass {
  final String? placeholder;

  DobInputClass({
    this.placeholder,
  });

  factory DobInputClass.fromJson(Map<String, dynamic> json) => DobInputClass(
        placeholder: json["placeholder"],
      );

  Map<String, dynamic> toJson() => {
        "placeholder": placeholder,
      };
}

class GenderInputClass {
  final String? placeholder;
  final String? dropDownHeading;

  GenderInputClass({
    this.placeholder,
    this.dropDownHeading,
  });

  factory GenderInputClass.fromJson(Map<String, dynamic> json) =>
      GenderInputClass(
        placeholder: json["placeholder"],
        dropDownHeading: json["drop_down_heading"],
      );

  Map<String, dynamic> toJson() => {
        "placeholder": placeholder,
        "drop_down_heading": dropDownHeading,
      };
}

class Forms {
  final PolicyDetailsForm? policyDetailsForm;

  Forms({
    this.policyDetailsForm,
  });

  factory Forms.fromJson(Map<String, dynamic> json) => Forms(
        policyDetailsForm: json["policy_details_form"] == null
            ? null
            : PolicyDetailsForm.fromJson(json["policy_details_form"]),
      );

  Map<String, dynamic> toJson() => {
        "policy_details_form": policyDetailsForm?.toJson(),
      };
}

class PolicyDetailsForm {
  final BottomSheet? sumInsuredBottomSheet;
  final BottomSheet? deductiblesBottomSheet;

  PolicyDetailsForm({
    this.sumInsuredBottomSheet,
    this.deductiblesBottomSheet,
  });

  factory PolicyDetailsForm.fromJson(Map<String, dynamic> json) =>
      PolicyDetailsForm(
        sumInsuredBottomSheet: json["sum_insured_bottom_sheet"] == null
            ? null
            : BottomSheet.fromJson(json["sum_insured_bottom_sheet"]),
        deductiblesBottomSheet: json["deductibles_bottom_sheet"] == null
            ? null
            : BottomSheet.fromJson(json["deductibles_bottom_sheet"]),
      );

  Map<String, dynamic> toJson() => {
        "sum_insured_bottom_sheet": sumInsuredBottomSheet?.toJson(),
        "deductibles_bottom_sheet": deductiblesBottomSheet?.toJson(),
      };
}

class BottomSheet {
  final String? title;
  final String? subtitle;

  BottomSheet({
    this.title,
    this.subtitle,
  });

  factory BottomSheet.fromJson(Map<String, dynamic> json) => BottomSheet(
        title: json["title"],
        subtitle: json["subtitle"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "subtitle": subtitle,
      };
}

class FluffyWidgets {
  final EditsRefundNote? editsRefundNote;
  final EditsRefundNote? reviewChanges;

  FluffyWidgets({
    this.editsRefundNote,
    this.reviewChanges,
  });

  factory FluffyWidgets.fromJson(Map<String, dynamic> json) => FluffyWidgets(
        editsRefundNote: json["edits_refund_note"] == null
            ? null
            : EditsRefundNote.fromJson(json["edits_refund_note"]),
        reviewChanges: json["review_changes"] == null
            ? null
            : EditsRefundNote.fromJson(json["review_changes"]),
      );

  Map<String, dynamic> toJson() => {
        "edits_refund_note": editsRefundNote?.toJson(),
        "review_changes": reviewChanges?.toJson(),
      };
}

class EditsRefundNote {
  final String? note;

  EditsRefundNote({
    this.note,
  });

  factory EditsRefundNote.fromJson(Map<String, dynamic> json) =>
      EditsRefundNote(
        note: json["note"],
      );

  Map<String, dynamic> toJson() => {
        "note": note,
      };
}
