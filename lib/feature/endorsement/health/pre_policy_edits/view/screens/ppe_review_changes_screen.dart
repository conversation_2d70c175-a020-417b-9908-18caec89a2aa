import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/common/view/FullPageLoader.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_service_loading_screen.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_services_initial_screen.dart';
import 'package:acko_flutter/feature/car_journey/view/icon_app_bar.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_constants.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_utils.dart';
import 'package:acko_flutter/feature/endorsement/core/util/node_router.dart';
import 'package:acko_flutter/feature/endorsement/domain/models/hl_endorsement_copies.dart'
    as hlEndorsementCopies;
import 'package:acko_flutter/feature/endorsement/domain/repository/hl_endorsement_repository.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/models/health_jm_response.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_nodes.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/review_bloc/ppe_review_bloc.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/review_bloc/ppe_review_states.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_policy_changes_model.dart';
import 'package:acko_flutter/feature/endorsement/shared/widgets/pricing_footer.dart';
import 'package:acko_flutter/feature/endorsement/shared/widgets/review_changes.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/events/health_life/page_events/health_life_page_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';
import 'package:sprintf/sprintf.dart';
import 'package:utilities/constants/constants.dart';

class ReviewChangesScreen extends StatefulWidget {
  const ReviewChangesScreen({super.key});

  @override
  State<ReviewChangesScreen> createState() => _ReviewChangesScreenState();
}

class _ReviewChangesScreenState extends State<ReviewChangesScreen> {
  PPEReviewCubit? _cubit;
  FullPageLoader _fullPageLoader = FullPageLoader.instance;
  final HealthJourneyManagerUtils _healthJMUtil = HealthJourneyManagerUtils();
  bool enableContinueCta = false;
  hlEndorsementCopies.PpeReviewChangesScreen? rcPpeReviewChangesScreen;

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of<PPEReviewCubit>(context);
    rcPpeReviewChangesScreen = HlEndorsementRepository()
        .prePolicyEdits
        .view
        ?.screens
        ?.ppeReviewChangesScreen;
    _cubit?.getEditsData();
  }

  @override
  Widget build(BuildContext context) {
    if (rcPpeReviewChangesScreen == null) {
      return SizedBox.shrink();
    }
    return BlocConsumer<PPEReviewCubit, PPEReviewState>(
        listenWhen: (prev, curr) =>
            curr is DocUploading ||
            curr is DocUploaded ||
            curr is NavigateNext ||
            curr is FullPageLoading ||
            curr is FullPageLoaded ||
            curr is ErrorToast ||
            curr is CtaStateChange ||
            curr is Loaded,
        listener: (context, state) {
          if (state is FullPageLoading) {
            _fullPageLoader.showFullPageLoader(context);
          } else if (state is FullPageLoaded) {
            _fullPageLoader.dismissFullPageLoader(context);
          }
          if (state is DocUploading) {
            _fullPageLoader.showFullPageLoader(context);
          } else if (state is DocUploaded) {
            _fullPageLoader.dismissFullPageLoader(context);
          } else if (state is NavigateNext) {
            Map<String, dynamic> args = {
              "proposal_id": _cubit?.proposalId,
              "data": _cubit?.prePolicyEditingResponse
            };

            if (_cubit!.nextNode! == PrePolicyEditNodes.SUMMARY) {
              Navigator.pushNamed(context, Routes.PPE_PAYMENT_SUCCESS_SCREEN,
                  arguments: args);
              return;
            }

            JourneyManagerRouter()
                .routeNodeToNativeRoutes(_cubit!.nextNode!, args, context);
          } else if (state is Loaded) {
            enableContinueCta = state.enableCta;
            context
                .read<PPEReviewCubit>()
                .triggerPageEvent(HLPageEvents.VIEW_REVIEW_CHANGES_PAGE);
          } else if (state is CtaStateChange) {
            setState(() {
              enableContinueCta = state.enableCta;
            });
          } else if (state is ErrorToast) {
            _healthJMUtil.showToastMessage(message: "Something went wrong");
          }
        },
        buildWhen: (prev, curr) =>
            curr is Loading || curr is Loaded || curr is Error,
        builder: (context, state) {
          if (state is Loaded)
            return _buildLoadedView(state.policyChangesData, state.documents,
                state.totalEdits ?? 0, state.draftState, state.isEditable);
          else if (state is Loading)
            return _getLoadingView();
          else
            return _getErrorView();
        });
  }

  Widget _getLoadingView() {
    return Scaffold(body: Center(child: AckoServiceLoadingScreen()));
  }

  Widget _getErrorView() {
    return Scaffold(
      body: AckoServicesIntiailScreen(
        title: something_went_wrong,
        subTitle: api_something_went_wrong_sory,
        btnTitle: go_back,
        isOutlinedButton: true,
        onTap: () => Navigator.pop(context),
        imgUrl: Util.getAssetImage(assetName: 'ic_bucket_drop.svg'),
      ),
    );
  }

  _buildLoadedView(
      PolicyChangesData? policyChangesData,
      List<Documents>? allDocuments,
      int totalEdits,
      DraftState draftState,
      bool isEditable) {
    return Scaffold(
      appBar: IconAppbar.newBackBtnAppBar(context, background: colorFFFFFF),
      bottomNavigationBar: PricingFooter(
        title: policyChangesData?.getFooterTitle(_cubit?.prePolicyEditingResponse?.edit?.entityDetails?.basbaProposal ?? false),
        price: policyChangesData?.footerAmountToBePaid,
        onContinue: () {
          _cubit?.onReviewContinue();
        },
        disabledOnPress: () {
          _healthJMUtil.showToastMessage(
              message: policyChangesData?.hasCheckBox ?? false
                  ? rcPpeReviewChangesScreen!.uploadDocAndCheckbox ?? ''
                  : rcPpeReviewChangesScreen!.uploadDoc ?? '');
        },
        enableCta: enableContinueCta,
        ctaText: policyChangesData?.getReviewCtaText(_cubit?.mandateCreationFlow ?? false) ?? continue_string
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SDUIText(
              value: (policyChangesData?.hasDocUploadRequired ?? false)
                  ? rcPpeReviewChangesScreen!.reviewAndUpload ?? ''
                  : rcPpeReviewChangesScreen!.review ?? '',
              textStyle: "hSmall",
              textColor: color040222,
              maxLines: 5,
            ),
            SizedBox(
              height: 4,
            ),
            SDUIText(
              value: sprintf(
                rcPpeReviewChangesScreen!.totalEdits ?? '',
                [totalEdits],
              ),
              textStyle: "pSmall",
              textColor: color5B5675,
            ),

            /// changes card
            if (policyChangesData?.portingChanges != null) ...[
              const SizedBox(
                height: 16,
              ),
              ReviewChangesWidget(
                title: rcPpeReviewChangesScreen!.portingDetails ?? '',
                changesData: [policyChangesData?.portingChanges],
                showUploadDoc: true,
                checkboxValue: _cubit?.checkboxValue,
                editable: isEditable,
                docUploadSuccess: (doc) {
                  _cubit?.uploadDoc(doc, "Porting details");
                },
                docDeleteSuccess: (doc) {
                  _cubit?.deleteDoc(doc, "Porting details");
                },
                proposalId: _cubit?.proposalId,
                entityReferenceId: _cubit?.entityReferenceId,
                onEditTap: () {
                  navigateToEditFlow(editType: EditType.PORTING);
                },
              ),
            ],
            if (policyChangesData?.policyChanges.isNotNullOrEmpty ?? false) ...[
              const SizedBox(
                height: 16,
              ),
              ReviewChangesWidget(
                title: rcPpeReviewChangesScreen!.policyDetails ?? '',
                changesData: policyChangesData?.policyChanges,
                showUploadDoc: true,
                checkboxValue: _cubit?.checkboxValue,
                editable: isEditable,
                docUploadSuccess: (doc) {
                  _cubit?.uploadDoc(doc, "Policy details");
                },
                docDeleteSuccess: (doc) {
                  _cubit?.deleteDoc(doc, "Policy details");
                },
                proposalId: _cubit?.proposalId,
                entityReferenceId: _cubit?.entityReferenceId,
                onEditTap: () {
                  navigateToEditFlow(editType: EditType.POLICY);
                },
              ),
            ],
            if (policyChangesData?.memberChanges.isNotNullOrEmpty ?? false) ...[
              const SizedBox(
                height: 16,
              ),
              ReviewChangesWidget(
                title: rcPpeReviewChangesScreen!.memberDetails ?? '',
                changesData: policyChangesData?.memberChanges,
                showUploadDoc: true,
                checkboxValue: _cubit?.checkboxValue,
                editable: isEditable,
                hasCheckBox: policyChangesData?.hasCheckBox ?? false,
                updateCheckboxValue: updateCheckboxValue,
                docUploadSuccess: (doc) {
                  _cubit?.uploadDoc(doc, "Member details");
                },
                docDeleteSuccess: (doc) {
                  _cubit?.deleteDoc(doc, "Member details");
                },
                proposalId: _cubit?.proposalId,
                entityReferenceId: _cubit?.entityReferenceId,
                onEditTap: () {
                  navigateToEditFlow(editType: EditType.MEMBER);
                },
              )
            ],
            if (policyChangesData?.updatedMembersList.isNotNullOrEmpty ??
                false) ...[
              const SizedBox(
                height: 16,
              ),
              ReviewChangesWidget(
                title: rcPpeReviewChangesScreen!.memberAdditionRemoval ?? '',
                changesData: policyChangesData?.updatedMembersList,
                showUploadDoc: true,
                checkboxValue: _cubit?.checkboxValue,
                editable: isEditable,
                docUploadSuccess: (doc) {
                  _cubit?.uploadDoc(doc, "Member addition/removal");
                },
                docDeleteSuccess: (doc) {
                  _cubit?.deleteDoc(doc, "Member addition/removal");
                },
                proposalId: _cubit?.proposalId,
                entityReferenceId: _cubit?.entityReferenceId,
                onEditTap: () {
                  navigateToEditFlow(editType: EditType.MEMBER);
                },
                updateCheckboxValue: updateCheckboxValue,
              )
            ],
            SizedBox(
              height: 20,
            ),
          ],
        ),
      ),
    );
  }

  void updateCheckboxValue(bool newValue) {
    _cubit?.updateCheckBoxValue(newValue);
  }

  navigateToEditFlow({EditType? editType}) {
    bool foundPpeEditScreen = false;

    Navigator.of(context).popUntil((route) {
      if (route.settings.name == Routes.PPE_EDIT_SCREEN) {
        foundPpeEditScreen = true;
        return true;

        /// Stop popping
      } else if (route.settings.name == Routes.PPE_EDIT_OVERVIEW_SCREEN ||
          route.settings.name == Routes.APP_HOME) {
        return true;

        /// Stop popping at APP_HOME
      } else {
        return false;

        /// Continue popping
      }
    });

    if (!foundPpeEditScreen) {
      Navigator.of(context).pushNamed(Routes.PPE_EDIT_SCREEN, arguments: {
        "proposal_id": _cubit?.proposalId,
        "edit_type": editType ?? EditType.NONE
      });
    }
  }
}
