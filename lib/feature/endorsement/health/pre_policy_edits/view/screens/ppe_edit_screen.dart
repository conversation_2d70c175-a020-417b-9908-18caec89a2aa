import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/common/view/FullPageLoader.dart';
import 'package:acko_flutter/common/view/app_bar.dart';
import 'package:acko_flutter/common/view/dashed_line.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_service_loading_screen.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_services_initial_screen.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_constants.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_utils.dart';
import 'package:acko_flutter/feature/endorsement/core/util/node_router.dart';
import 'package:acko_flutter/feature/endorsement/domain/models/hl_endorsement_copies.dart'
    as hlEndorsementCopies;
import 'package:acko_flutter/feature/endorsement/domain/repository/hl_endorsement_repository.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_nodes.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_repo.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/edit_bloc/ppe_edit_bloc.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/edit_bloc/ppe_edit_states.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_form_editing_models.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/validation_models/family_constrains.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/bottomsheets/ppe_review_changes_sheet.dart';
import 'package:acko_flutter/feature/endorsement/shared/forms/ppe_member_details_form.dart';
import 'package:acko_flutter/feature/endorsement/shared/forms/ppe_policy_details_form.dart';
import 'package:acko_flutter/feature/endorsement/shared/forms/ppe_porting_details_form.dart';
import 'package:acko_flutter/feature/endorsement/shared/widgets/custom_expansion_tile.dart';
import 'package:acko_flutter/feature/endorsement/shared/widgets/pricing_footer.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/utils.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:design_module/design_module.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/state_provider/StateProvider.dart';
import 'package:utilities/widgets/acko_will_pop_scope.dart';

class PrePolicyEditsScreen extends StatefulWidget {
  final String? title;
  final String? description;
  const PrePolicyEditsScreen({
    Key? key,
    this.title,
    this.description,
  }) : super(key: key);

  @override
  State<PrePolicyEditsScreen> createState() => _PrePolicyEditsScreenState();
}

class _PrePolicyEditsScreenState extends State<PrePolicyEditsScreen>
    implements StateListener {
  PPEEditCubit? _cubit;
  late FullPageLoader _fullPageLoader;
  StateProvider _stateProvider = StateProvider();
  bool isPricingLoading = false;
  final HealthJourneyManagerUtils _healthJMUtil = HealthJourneyManagerUtils();

  ScrollController _scrollController = ScrollController();
  hlEndorsementCopies.PpeEditScreen? rcPpeEditScreen;
  bool refreshOverview = false;

  @override
  void initState() {
    super.initState();
    _stateProvider.subscribe(this);
    _fullPageLoader = FullPageLoader.instance;
    rcPpeEditScreen =
        HlEndorsementRepository().prePolicyEdits.view?.screens?.ppeEditScreen;
    _cubit = BlocProvider.of<PPEEditCubit>(context);
    _scrollController.addListener(() {
      _cubit?.scrollOffset = _scrollController.offset;
    });
    _cubit?.getEditsData();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToUpdatedForm(_cubit?.scrollOffset ?? 0.0);
    });
  }

  @override
  void onStateChanged(ObserverState state, {data}) {
    if (state == ObserverState.REFRESH_PPE_EDIT) {
      _cubit?.scrollOffset = _scrollController.position.maxScrollExtent;
      _cubit?.getEditsData(force: true);
    }
  }

  @override
  dispose() {
    super.dispose();
    _scrollController.dispose();
    _stateProvider.dispose(this);
  }

  void _scrollToUpdatedForm(double offset, {int index = 0}) {
    if (_scrollController.hasClients)
      _scrollController.animateTo(
        offset,
        duration: Duration(seconds: 1),
        curve: Curves.easeInOut,
      );

    return;
  }

  @override
  Widget build(BuildContext context) {
    if (rcPpeEditScreen == null) {
      return SizedBox.shrink();
    }
    return BlocConsumer<PPEEditCubit, PPEEditState>(
      listenWhen: (prev, curr) =>
          curr is Loaded ||
          curr is Loading ||
          curr is Error ||
          curr is ShowFormEditingLoader ||
          curr is DismissFormEditingLoader ||
          curr is ErrorToast ||
          curr is ReviewSheetLoaded ||
          curr is AutoScroll,
      listener: (context, state) {
        if (state is Loaded) {
          if (state.refreshOverview == true) {
            refreshOverview = true;
          }
          if (state.addMemberFlow) {
            // todo: use node router
            Navigator.pushNamed(context, Routes.PPE_ADD_MEMBER_SCREEN,
                arguments: {
                  "proposal_id": _cubit?.proposalId,
                  "journey_type": JourneyType.PRE_POLICY_EDIT,
                  "data": _cubit?.prePolicyEditingResponse
                }).then(
                (value) => _scrollToUpdatedForm(_cubit?.scrollOffset ?? 0.0));
          }
          if (state.redirectToNextNode) {
            JourneyManagerRouter().routeNodeToNativeRoutes(
                _cubit!.nextNode?.value.isNotNullOrEmpty ?? false
                    ? _cubit!.nextNode!
                    : PrePolicyEditNodes.REVIEW,
                {
                  "proposal_id": _cubit?.proposalId,
                  "data": _cubit?.prePolicyEditingResponse
                },
                context);
          }
        }
        if (state is AutoScroll) {
          if (state.scrollToBottom)
            _scrollToUpdatedForm(_scrollController.position.maxScrollExtent);
          else
            _scrollToUpdatedForm(_cubit?.scrollOffset ?? 0.0);
        }
        if (state is ShowFormEditingLoader) {
          if (state.fullPageLoader) {
            _fullPageLoader.showFullPageLoader(context);
          } else {
            setState(() {
              isPricingLoading = true;
            });
          }
        }
        if (state is DismissFormEditingLoader) {
          if (state.fullPageLoader) {
            _fullPageLoader.dismissFullPageLoader(context);
          } else {
            setState(() {
              isPricingLoading = false;
            });
          }
        }
        if (state is ErrorToast) {
          _healthJMUtil.showToastMessage(message: "Something went wrong");
        }
        if (state is ReviewSheetLoaded) {
          context.showAckoModalBottomSheet(
              child: ReviewChangesSheet(
            policyChangesData: state.policyChangesData,
            totalEdits: state.totalEdits,
            isBasbaProposal: _cubit
                ?.prePolicyEditingResponse?.edit?.entityDetails?.basbaProposal,
          ));
        }
      },
      buildWhen: (prev, curr) =>
          curr is Loaded || curr is Loading || curr is Error,
      builder: (context, state) {
        if (state is Loaded)
          return _buildLoadedState(state.formValues, state.amount,
              state.enableCta, state.totalEdits, state.validationConfig, state.showAddMore);
        else if (state is Loading)
          return _getLoadingView();
        else
          return _getErrorView();
      },
    );
  }

  Widget _getLoadingView() {
    return Scaffold(body: Center(child: AckoServiceLoadingScreen()));
  }

  Widget _getErrorView() {
    return Scaffold(
      body: AckoServicesIntiailScreen(
        title: something_went_wrong,
        subTitle: api_something_went_wrong_sory,
        btnTitle: go_back,
        isOutlinedButton: true,
        onTap: () => Navigator.pop(context),
        imgUrl: Util.getAssetImage(assetName: 'ic_bucket_drop.svg'),
      ),
    );
  }

  Future<bool> _popPage() async {
    if (refreshOverview)
      _stateProvider.notify(ObserverState.REFRESH_PPE_OVERVIEW);
    Navigator.pop(context);
    return Future.value(true);
  }

  _buildLoadedState(FormValues? formValues, int? amount, bool? enableCta,
      int? totalEdits, EndorsementFormValidatorConfig validationConfig, bool showAddMoreCta) {
    return AckoWillPopScope(
      onWillPop: _popPage,
      child: Scaffold(
        appBar: getAppBar('', context,
            elevation: 0.0, bgColor: colorFFFFFF, backBtnCallback: _popPage),
        bottomNavigationBar: Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: _getPricingFooter(
              amount: amount, enableCta: enableCta, totalEdits: totalEdits, isBasbaProposal: _cubit?.prePolicyEditingResponse?.edit?.entityDetails?.basbaProposal),
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            controller: _scrollController, // Attach ScrollController
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SDUIText(
                  value: widget.title ?? rcPpeEditScreen!.title ?? '',
                  maxLines: 10,
                  textStyle: "hMedium",
                  textColor: color040222,
                  alignment: TextAlign.start,
                ),
                const SizedBox(height: 8),
                SDUIText(
                  value:
                      widget.description ?? rcPpeEditScreen!.description ?? '',
                  maxLines: 10,
                  textStyle: "pMedium",
                  textColor: color5B5675,
                ),
                if ((formValues?.oldValues?.portingDetails?.portingDate
                            .isNotNullOrEmpty ??
                        false) ||
                    (formValues?.newValues?.portingDetails?.portingDate
                            .isNotNullOrEmpty ??
                        false)) ...[
                  const SizedBox(height: 24),
                  CustomExpansionTile(
                    title: rcPpeEditScreen!.portingDetails ?? '',
                    content: PortingDetailsForm(
                      oldPortingDetails: formValues?.oldValues?.portingDetails,
                      newPortingDetails: formValues?.newValues?.portingDetails,
                      updatePortingDetails: (String key, String newValue) =>
                          _cubit?.updatePortingDetails(
                              key: key, newValue: newValue),
                      lastAllowedDate: rcPpeEditScreen!.portingExpiryDaysLimit ?? 90,
                    ),
                    initiallyExpanded: _cubit?.editType == EditType.PORTING,
                    analyticalEvent: () {
                      _cubit?.triggerTapEvent(
                          HLTrackEvents.TAP_PORTING_DETAILS_TAB);
                    },
                  ),
                ],
                const SizedBox(height: 24),
                CustomExpansionTile(
                  title: rcPpeEditScreen!.policyDetails ?? '',
                  content: PolicyDetailsForm(
                    oldPolicyDetails: formValues?.oldValues?.policyDetails,
                    newPolicyDetails: formValues?.newValues?.policyDetails,
                    updatePolicyDetails: (String key, String newValue) => _cubit
                        ?.updatePolicyDetails(key: key, newValue: newValue),
                  ),
                  initiallyExpanded: _cubit?.editType == EditType.POLICY,
                  analyticalEvent: () {
                    _cubit
                        ?.triggerTapEvent(HLTrackEvents.TAP_POLICY_DETAILS_TAB);
                  },
                ),
                const SizedBox(height: 24),
                CustomExpansionTile(
                  title: rcPpeEditScreen!.memberDetails ?? '',
                  content: Column(
                    children: [
                      MemberDetailsForm(
                        oldMemberDetails:
                            formValues?.oldValues?.proposerDetails,
                        newMemberDetails:
                            formValues?.newValues?.proposerDetails,
                        memberIndex: 1,
                        updateMemberDetails: (String key,
                                String insuredId,
                                String newValue,
                                bool isProposer,
                                bool hasError,
                                bool isFinancial) =>
                            _cubit?.findAndUpdateMemberDetails(
                                key: key,
                                insuredId: insuredId,
                                newValue: newValue,
                                isProposer: isProposer,
                                hasError: hasError,
                                isFinancial: isFinancial),
                        addMemberBack: (String insuredId) =>
                            _cubit?.addMemberBack(insuredId),
                        removeMember: (String insuredId) =>
                            _cubit?.removeMember(insuredId),
                        validationConfig: validationConfig,
                      ),
                      // list of members

                      if (formValues
                              ?.newValues?.memberDetails.isNotNullOrEmpty ??
                          false)
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: DottedLine(
                            dashColor: colorE7E7F0,
                          ),
                        ),
                      ListView.separated(
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) => MemberDetailsForm(
                                oldMemberDetails:
                                    formValues?.oldValues?.memberDetails !=
                                                null &&
                                            index >= 0 &&
                                            index <
                                                formValues!.oldValues!
                                                    .memberDetails!.length
                                        ? formValues
                                            .oldValues!.memberDetails![index]
                                        : null,
                                newMemberDetails: formValues
                                    ?.newValues?.memberDetails?[index],
                                memberIndex: index + 2,
                                updateMemberDetails: (String key,
                                        String insuredId,
                                        String newValue,
                                        bool isProposer,
                                        bool hasError,
                                        bool isFinancial) =>
                                    _cubit?.findAndUpdateMemberDetails(
                                        key: key,
                                        insuredId: insuredId,
                                        newValue: newValue,
                                        isProposer: isProposer,
                                        hasError: hasError,
                                        isFinancial: isFinancial),
                                addMemberBack: (String insuredId) =>
                                    _cubit?.addMemberBack(insuredId),
                                removeMember: (String insuredId) =>
                                    _cubit?.removeMember(insuredId),
                                validationConfig: validationConfig,
                              ),
                          separatorBuilder: (context, index) => Padding(
                                padding: const EdgeInsets.all(16),
                                child: DottedLine(
                                  dashColor: colorE7E7F0,
                                ),
                              ),
                          itemCount:
                              formValues?.newValues?.memberDetails?.length ??
                                  0),
                      if (formValues?.newValues?.memberDetails.isNullOrEmpty ??
                          true)
                        SizedBox(height: 16),
                      if (showAddMoreCta)
                        _buildAddNewMember(totalEdits)
                    ],
                  ),
                  initiallyExpanded: _cubit?.editType == EditType.MEMBER,
                  analyticalEvent: () {
                    _cubit
                        ?.triggerTapEvent(HLTrackEvents.TAP_MEMBER_DETAILS_TAB);
                  },
                ),
                const SizedBox(height: 48),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _getPricingFooter({int? amount, bool? enableCta, int? totalEdits, bool? isBasbaProposal}) {
    // if(totalEdits == null || totalEdits == 0) return SizedBox.shrink();
    if (isPricingLoading == false) {
      final title = (amount != null && amount != 0)
          ? (amount < 0 ? (isBasbaProposal == true ? 'Release amount' : 'Refund amount') : 'Additional premium for ' + (totalEdits != null && totalEdits > 1 ? 'edits' : 'edit'))
          : null;
      final price = (totalEdits ?? 0) > 0
          ? (amount != null
              ? (amount < 0
                  ? '₹${NumberUtils.commaSeparatedNumber(amount.abs())}'
                  : '₹${NumberUtils.commaSeparatedNumber(amount)}')
              : '')
          : '';
      return PricingFooter(
        title: title,
        price: price,
        totalEdits: totalEdits,
        onTotalEditsTap: () {
          _cubit?.getPolicyChangesData();
        },
        onContinue: () => _cubit?.submitEditingForm(),
        enableCta: enableCta!,
        ctaText: "Continue",
      );
    } else if (isPricingLoading == true) {
      return PricingFooterSkeleton();
    }
  }

  _buildAddNewMember(int? totalEdits) {
    return Padding(
      padding: EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: GestureDetector(
        onTap: () {
          if (totalEdits != null && totalEdits > 0) {
            _cubit?.syncApiAndFormValues(
                addMemberFlow: true,
                nextNode: PrePolicyEditNodes.PREMIUM,
                fullPageLoader: true);
          } else {
            Navigator.pushNamed(context, Routes.PPE_ADD_MEMBER_SCREEN,
                arguments: {
                  "proposal_id": _cubit?.proposalId,
                  "journey_type": JourneyType.PRE_POLICY_EDIT,
                  "data": _cubit?.prePolicyEditingResponse
                });
          }
        },
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Icon(Icons.add_circle_outline_rounded,
                color: color1B73E8, size: 16),
            SDUIText(
              value: rcPpeEditScreen!.addMembers ?? '',
              textStyle: "lSmall",
              textColor: color1B73E8,
              padding: EdgeInsets.only(left: 4),
            ),
          ],
        ),
      ),
    );
  }
}
