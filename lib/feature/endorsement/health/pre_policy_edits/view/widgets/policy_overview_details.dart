import 'package:acko_flutter/common/util/AckoTextStyle.dart';
import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/dashed_line.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_constants.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_utils.dart';
import 'package:acko_flutter/feature/endorsement/domain/models/hl_endorsement_copies.dart';
import 'package:acko_flutter/feature/endorsement/domain/repository/hl_endorsement_repository.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_form_editing_models.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_policy_overview_model.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';

class PolicyOverviewDetails extends StatelessWidget {
  final EditType editType;
  final PolicyOverview? policyOverviewData;

  const PolicyOverviewDetails(
      {super.key, required this.editType, required this.policyOverviewData});

  @override
  Widget build(BuildContext context) {
    final rcPolicyOverviewDetails = HlEndorsementRepository()
        .prePolicyEdits
        .view
        ?.widgets
        ?.policyOverviewDetails;
    switch (editType) {
      case EditType.PORTING:
        return _buildPortingDetailsView(policyOverviewData?.portingDetails,
            rcPolicyOverviewDetails!.portingDetailsView);
      case EditType.POLICY:
        return _buildPolicyDetailsView(policyOverviewData?.policyDetails,
            rcPolicyOverviewDetails!.policyDetailsView);
      case EditType.MEMBER:
        return _buildMemberDetailsView(
            policyOverviewData?.proposerDetails,
            policyOverviewData?.memberDetails,
            rcPolicyOverviewDetails!.memberDetailsView);
      default:
        return SizedBox.shrink();
    }
  }

  _buildPortingDetailsView(PortingDetails? portingDetails,
      PortingDetailsView? rcPortingDetailsView) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(
            color: colorF5F5F5,
            height: 1,
          ),
          const SizedBox(
            height: 20,
          ),
          SDUIText(
            padding: EdgeInsets.symmetric(horizontal: 16),
            value: rcPortingDetailsView?.previousPolicyEndDate,
            textColor: color4B4B4B,
            textStyle: "pXSmall",
            maxLines: 5,
          ),
          const SizedBox(height: 4),
          SDUIText(
            padding: EdgeInsets.symmetric(horizontal: 16),
            value: HealthJourneyManagerUtils()
                .formatDate(portingDetails?.portingDate),
            textColor: color121212,
            textStyle: "lMedium",
            maxLines: 5,
          ),
          const SizedBox(
            height: 20,
          ),
        ],
      ),
    );
  }

  _buildPolicyDetailsView(
      PolicyDetails? policyDetails, PolicyDetailsView? rcPolicyDetailsView) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(
            color: colorF5F5F5,
            height: 1,
          ),
          const SizedBox(
            height: 20,
          ),
          SDUIText(
            padding: EdgeInsets.symmetric(horizontal: 16),
            value: rcPolicyDetailsView?.yourPlan,
            textColor: color4B4B4B,
            textStyle: "pXSmall",
            maxLines: 5,
          ),
          const SizedBox(height: 4),
          SDUIText(
            padding: EdgeInsets.symmetric(horizontal: 16),
            value: policyDetails?.formattedPackageName,
            textColor: color121212,
            textStyle: "lMedium",
            maxLines: 5,
          ),
          const SizedBox(
            height: 16,
          ),
          SDUIText(
            padding: EdgeInsets.symmetric(horizontal: 16),
            value: rcPolicyDetailsView?.sumInsured,
            textColor: color4B4B4B,
            textStyle: "pXSmall",
            maxLines: 5,
          ),
          const SizedBox(height: 4),
          SDUIText(
            padding: EdgeInsets.symmetric(horizontal: 16),
            value: policyDetails?.sumInsured?.name,
            textColor: color121212,
            textStyle: "lMedium",
            maxLines: 5,
          ),
          if (policyDetails?.deductible?.name.isNotNullOrEmpty ?? false) ...[
            SizedBox(
              height: 16,
            ),
            SDUIText(
              padding: EdgeInsets.symmetric(horizontal: 16),
              value: rcPolicyDetailsView?.deductible,
              textColor: color4B4B4B,
              textStyle: "pXSmall",
              maxLines: 5,
            ),
            const SizedBox(height: 4),
            SDUIText(
              padding: EdgeInsets.symmetric(horizontal: 16),
              value: policyDetails?.deductible?.name,
              textColor: color121212,
              textStyle: "lMedium",
              maxLines: 5,
            ),
          ],
          const SizedBox(
            height: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildMemberDetailsView(
      MemberDetails? proposerDetails,
      List<MemberDetails?>? memberDetails,
      MemberDetailsView? rcMemberDetailsView) {
    return Column(
      children: [
        const Divider(
          color: colorF5F5F5,
          height: 1,
        ),
        const SizedBox(
          height: 20,
        ),
        _buildSingleMemberView(
            proposerDetails, rcMemberDetailsView?.singleMemberView),
        if (memberDetails != null)
          ListView.builder(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: memberDetails.length,
            itemBuilder: (context, index) {
              return Column(
                children: [
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                    child: DottedLine(
                      dashColor: colorE7E7F0,
                    ),
                  ),
                  _buildSingleMemberView(memberDetails[index],
                      rcMemberDetailsView?.singleMemberView),
                ],
              );
            },
          ),
        const SizedBox(
          height: 20,
        ),
      ],
    );
  }

  Widget _buildSingleMemberView(
      MemberDetails? details, SingleMemberView? rcSingleMemberView) {
    if (details == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SDUIImage(
              imageUrl: HealthJourneyManagerAssets.memberIcon,
              width: 20,
              height: 20,
            ),
            const SizedBox(width: 4),
            Flexible(
              child: SDUIText(
                mainAxisAlignment: MainAxisAlignment.start,
                alignment: TextAlign.start,
                textStyle: "hMedium",
                textConfiguration: TextConfiguration.rich,
                childWidgets: [
                  SDUIText(
                    value: details.name,
                    textColor: color121212,
                    textStyle: "hXXSmall",
                    maxLines: 5,
                  ),
                  if (details.isProposer)
                    SDUIText(
                      value: " (Proposer)",
                      textColor: color4B4B4B,
                      textStyle: "hXXSmall",
                      maxLines: 5,
                    ),
                ],
              ),
            ),
          ],
        ),
        if (details.isProposer) ...[
          const SizedBox(height: 4),
          Text(
            rcSingleMemberView?.proposerNote ?? "",
            style: TextStyleInterRegular(
                color: colorD16900, fontStyle: FontStyle.italic),
          ),
        ],
        if (!details.isProposer && details.relation.isNotNullOrEmpty) ...[
          const SizedBox(height: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SDUIText(
                value: rcSingleMemberView?.relationship,
                textColor: color4B4B4B,
                textStyle: "pXSmall",
                maxLines: 5,
              ),
              const SizedBox(height: 4),
              SDUIText(
                value: details.relation!,
                textColor: color121212,
                textStyle: "lMedium",
                maxLines: 5,
              ),
            ],
          ),
        ],
        if (details.dateOfBirth.isNotNullOrEmpty ||
            details.gender.isNotNullOrEmpty)
          const SizedBox(height: 12),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (details.dateOfBirth.isNotNullOrEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SDUIText(
                    value: rcSingleMemberView?.dob,
                    textColor: color4B4B4B,
                    textStyle: "pXSmall",
                    maxLines: 5,
                  ),
                  const SizedBox(height: 4),
                  SDUIText(
                    value: HealthJourneyManagerUtils()
                        .formatDate(details.dateOfBirth),
                    textColor: color121212,
                    textStyle: "lMedium",
                    maxLines: 5,
                  ),
                ],
              ),
            if (details.gender.isNotNullOrEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  SDUIText(
                    value: rcSingleMemberView?.gender,
                    textColor: color4B4B4B,
                    textStyle: "pXSmall",
                    maxLines: 5,
                  ),
                  const SizedBox(height: 4),
                  SDUIText(
                    value: details.gender!.toSentenceCase(),
                    textColor: color121212,
                    textStyle: "lMedium",
                    maxLines: 5,
                  ),
                ],
              ),
          ],
        ),
        if (details.height.isNotNullOrEmpty || details.weight.isNotNullOrEmpty)
          const SizedBox(height: 12),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (details.height.isNotNullOrEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SDUIText(
                    value: rcSingleMemberView?.height,
                    textColor: color4B4B4B,
                    textStyle: "pXSmall",
                    maxLines: 5,
                  ),
                  const SizedBox(height: 4),
                  SDUIText(
                    value: "${details.height} ft",
                    textColor: color121212,
                    textStyle: "lMedium",
                    maxLines: 5,
                  ),
                ],
              ),
            if (details.weight.isNotNullOrEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  SDUIText(
                    value: rcSingleMemberView?.weight,
                    textColor: color4B4B4B,
                    textStyle: "pXSmall",
                    maxLines: 5,
                  ),
                  const SizedBox(height: 4),
                  SDUIText(
                    value: "${details.weight} kg",
                    textColor: color121212,
                    textStyle: "lMedium",
                    maxLines: 5,
                  ),
                ],
              ),
          ],
        ),
        if (details.pinCode.isNotNullOrEmpty) ...[
          const SizedBox(height: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SDUIText(
                value: rcSingleMemberView?.pinCode,
                textColor: color4B4B4B,
                textStyle: "pXSmall",
                maxLines: 5,
              ),
              const SizedBox(height: 4),
              SDUIText(
                value: details.pinCode!,
                textColor: color121212,
                textStyle: "lMedium",
                maxLines: 5,
              ),
            ],
          ),
        ],
        if (details.isProposer) ...[
          if (details.mobileNumber.isNotNullOrEmpty) ...[
            const SizedBox(height: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SDUIText(
                  value: rcSingleMemberView?.mobileNumber,
                  textColor: color4B4B4B,
                  textStyle: "pXSmall",
                  maxLines: 5,
                ),
                const SizedBox(height: 4),
                SDUIText(
                  value: details.mobileNumber!,
                  textColor: color121212,
                  textStyle: "lMedium",
                  maxLines: 5,
                ),
              ],
            ),
          ],
          if (details.email.isNotNullOrEmpty) ...[
            const SizedBox(height: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SDUIText(
                  value: rcSingleMemberView?.email,
                  textColor: color4B4B4B,
                  textStyle: "pXSmall",
                  maxLines: 5,
                ),
                const SizedBox(height: 4),
                SDUIText(
                  value: details.email!,
                  textColor: color121212,
                  textStyle: "lMedium",
                  maxLines: 5,
                ),
              ],
            ),
          ],
        ],
      ]),
    );
  }
}
