import 'dart:async';

import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/auto_assets/bloc/create_asset_bloc.dart';
import 'package:acko_flutter/feature/auto_assets/bloc/create_asset_bloc_events.dart';
import 'package:acko_flutter/feature/auto_assets/model/asset_search_model.dart';
import 'package:acko_flutter/feature/auto_assets/view/create_asset/error_state.dart';
import 'package:acko_flutter/feature/auto_assets/view/create_asset/vehicle_already_added_bottomSheet.dart';
import 'package:acko_flutter/feature/auto_assets/view/create_asset/vehicle_confirmation_card.dart';
import 'package:acko_flutter/feature/car_journey/view/icon_app_bar.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:lottie/lottie.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/state_provider/StateProvider.dart';

import '../../../../common/util/bottom_insets_observer.dart';
import '../../../../common/util/color_constants.dart';
import '../../../../common/util/validator.dart';
import '../../../../common/view/AckoText.dart';
import '../../../../common/view/acko_text_field.dart';
import '../../../../common/view/auto_size_bottom_sheet.dart';
import '../../../../r2d2/events.dart';
import '../../../../util/Utility.dart';
import '../../../../util/health/utils.dart';
import '../../../acko_services/ui/acko_service_loading_screen.dart';
import '../../bloc/auto_assets_controller_bloc.dart';
import '../../model/detail_vehicle_asset_model.dart';

class CreateVehicleAsset extends StatefulWidget {
  final String? regNumber;
  final String? source;

  CreateVehicleAsset({Key? key, this.regNumber, this.source}) : super(key: key);
  @override
  State<CreateVehicleAsset> createState() => _CreateVehicleAssetState();
}

class _CreateVehicleAssetState extends State<CreateVehicleAsset> {
  Size? _size;
  TextEditingController _controller = TextEditingController();
  String? _carNumberError;
  ScrollController _scrollController = ScrollController();
  FocusNode _focusNode = FocusNode();
  late BottomInsetObserver _insetObserver;
  SearchAssetBloc? _createAssetBloc;
  bool _isUserOnInitSearchState = true;
  List<Map<String, dynamic>> _vehicleInfoOptions = [
    {'text': 'Timely traffic challan alerts', 'icon': 'add_vehicle_info_1.svg'},
    {
      'text': 'Access vehicle details & documents easily',
      'icon': 'add_vehicle_info_2.svg'
    },
    {
      'text': 'Personalized offers and quotes for your vehicle',
      'icon': 'add_vehicle_info_3.svg'
    },
  ];

  @override
  void initState() {
    super.initState();
    _initUserAssetsBloc();
    _insetObserver = BottomInsetObserver()
      ..addListener(_keyboardVisibiltyStateListener);
    if (widget.regNumber.isNotNullOrEmpty) _initData();
    // _focusNode.addListener(_textFieldFocusListener);
  }

  void _keyboardVisibiltyStateListener(BottomInsetChanges change) {
    if (change.currentInset > 0) {
      if (mounted && isScrollControllerLinkedToView)
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
    }
  }

  _initUserAssetsBloc() {
    BlocProvider.of<UserAssetsControllerBloc>(context).initAutoAssets();
  }

  _textFieldFocusListener() {
    if (_focusNode.hasFocus)
      Future.delayed(Duration(milliseconds: 1000), () {
        if (mounted && isScrollControllerLinkedToView)
          _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      });
  }

  _initData() {
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        _controller.text = (widget.regNumber ?? "").trim();
        _onContinueTapped();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    _createAssetBloc = BlocProvider.of<SearchAssetBloc>(context);
    _size = MediaQuery.of(context).size;
    return WillPopScope(
      onWillPop: () async {
        if (_isUserOnInitSearchState || widget.regNumber.isNotNullAndEmpty)
          return Future.value(true);
        _createAssetBloc?.resetToInitState();
        return Future.value(false);
      },
      child: Scaffold(
        appBar: IconAppbar.newBackBtnAppBar(
          context,
          background: Colors.transparent,
        ),
        extendBodyBehindAppBar: true,
        backgroundColor: colorFFFFFF,
        body: _getBody(),
      ),
    );
  }

  _getBody() {
    return BlocConsumer<SearchAssetBloc, CreateAssetBlocState>(
        listener: (context, state) {
          if (state is AssetAlreadyExistBottomSheet) {
            _showRecordAlreadyExistsBottomSheet();
          } else if (state is RateLimitExceeded) {
            NavigationAction.fromJson(state.rateLimitPayload!)
              ..executeAction(context);
          }
        },
        buildWhen: (prevState, newState) =>
            (newState is! AssetAlreadyExistBottomSheet) &&
            (newState is! RateLimitExceeded),
        builder: (context, state) {
          if (state is InitialState) {
            _isUserOnInitSearchState = true;
            return _getDefaultState();
          } else if (state is LoadingState) {
            return _loadingState();
          } else if (state is ConfirmationState) {
            _controller.text = '';
            _isUserOnInitSearchState = false;
            return _confirmationState(state.model);
          } else if (state is ErrorState) {
            _controller.text = '';
            _isUserOnInitSearchState = false;
            return _errorState(state);
          }
          return SizedBox.shrink();
        });
  }

  _showRecordAlreadyExistsBottomSheet() {
    List<DetailVehicleAssetModel>? _vehiclesList =
        BlocProvider.of<UserAssetsControllerBloc>(context)
            .vehiclesList
            .where((element) =>
                (element.vehicle?.assetNumber ?? "").toLowerCase() ==
                _controller.text.trim().toLowerCase())
            .toList();
    if (_vehiclesList.isEmpty) {
      UiUtils.getInstance
          .showToast(somethingWentWrong, toastLength: Toast.LENGTH_LONG);
      return;
    }

    showModalBottomSheet(
        barrierColor: color040222.withOpacity(0.7),
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(15.0))),
        builder: (context) {
          return AutoSizeBottomSheet(
            content: VehicleAlreadyAddedBottomSheetContent(
                vehicleList: _vehiclesList,
                ctx: context,
                regNumber: _controller.text.trim().toLowerCase()),
          );
        });
  }

  _confirmationState(VehicleAssetModel model) => VehicleConfirmationCard(
        model: model,
        source: widget.source,
        onConfirmationButtonTapped: _onVehicleConfirmationTap,
        popBack: () {
          Navigator.popUntil(
              context, (route) => (route.settings.name == Routes.APP_HOME));
        },
        userAssetControllerBloc:
            BlocProvider.of<UserAssetsControllerBloc>(context),
      );

  void _onVehicleConfirmationTap(DetailVehicleAssetModel model) async {
    bool useV10Design = (await RemoteConfigInstance.instance
            .getGbAsyncData(RemoteConfigKeysSet.APP_IA_VERSION))
        .toString()
        .equalsIgnoreCase("app_v10");
    if (!useV10Design) {
      Navigator.pushReplacementNamed(context, Routes.SINGLE_ASSET_VIEW,
          arguments: {
            "showNewAssetAnimation": true,
            "assetId": model.assetId,
            "regNumber": model.vehicle?.assetNumber,
            "context": context
          });
    } else {
      StateProvider().notify(ObserverState.NEW_ASSET_ADDED, data: {
        "assetId": model.assetId,
        "regNumber": model.vehicle?.assetNumber,
      });
      Navigator.popUntil(
          context, (route) => (route.settings.name == Routes.APP_HOME));
      StateProvider().notify(ObserverState.CHANGE_TAB, data: {"index": 1});
    }
  }

  _errorState(ErrorState state) => CreateVehicleErrorState(
        onRetryTapped: onRetry,
        registrationNumber: _controller.text,
      );

  _loadingState() => SizedBox(
      width: _size!.width,
      height: _size!.height,
      child: Center(child: AckoServiceLoadingScreen()));

  _getDefaultState() => Stack(
        children: [
          GestureDetector(
            onTap: _removeFocus,
            child: SingleChildScrollView(
              controller: _scrollController,
              child: SizedBox(
                height: _size!.height,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    _getHeaderContent(),
                    _getSearchWidget(),
                  ],
                ),
              ),
            ),
          ),
        ],
      );

  _getHeaderContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: _size!.height * 0.25,
          width: _size!.width,
          decoration: BoxDecoration(
              gradient: LinearGradient(colors: [colorEFE9FB, colorE3FAFC])),
          child: Center(
            child: Stack(
              children: [
                SizedBox(
                    height: _size!.height * 0.25,
                    child: SvgPicture.asset(Util.getAssetImage(
                        assetName: 'create_asset_bg_circles.svg'))),
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: Center(
                    child: SizedBox(
                      height: _size!.height * 0.25 * 0.7,
                      width: _size!.height * 0.25 * 0.7,
                      child: Lottie.asset('assets/anim/car_bike_anim.json'),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        _getInfoText(),
      ],
    );
  }

  _getInfoText() => Padding(
        padding: const EdgeInsets.symmetric(vertical: 32.0, horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextEuclidBoldL18(
              add_your_vehicle,
              textColor: color000000,
              textSize: 22.0,
              textAlign: TextAlign.start,
            ),
            SizedBox(
              height: 24,
            ),
            ...List.generate(
                _vehicleInfoOptions.length,
                (index) => Padding(
                      padding: const EdgeInsets.only(bottom: 20.0),
                      child: Row(
                        children: [
                          SizedBox(
                              width: 24,
                              child: SvgPicture.asset(Util.getAssetImage(
                                  assetName: _vehicleInfoOptions[index]['icon']
                                      .toString()))),
                          SizedBox(
                            width: 12,
                          ),
                          Expanded(
                            child: TextEuclidRegular(
                              _vehicleInfoOptions[index]['text'].toString(),
                              textColor: color000000,
                              textSize: 14.0,
                            ),
                          ),
                        ],
                      ),
                    )),
          ],
        ),
      );

  _getSearchWidget() => Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: GetTextField().getIconTextFormField(
                _controller,
                1,
                _carNumberError,
                '',
                enter_car_number.replaceAll('car', 'vehicle'),
                focusNode: _focusNode,
                textStyle: TextStyle(
                  fontFamily: 'euclid_circularB_medium',
                  fontSize: 16.0,
                  color: color282E33,
                  fontWeight: FontWeight.w600,
                ),
                hintColor: color5B5675.withOpacity(0.5),
                capitalization: TextCapitalization.characters,
                verticalPadding: 20.0,
                horizontalPadding: 16,
                hintTextStyle: TextStyle(color: color5B5675, fontSize: 16.0),
                fillColor: Colors.white,
                borderColor: colorE7E7F0,
                focusedBorderColor: color5B5675),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 24),
            child: ElevatedButton(
              onPressed: _onContinueTapped,
              child: Center(
                child: Text(
                  txt_continue,
                  style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: colorFFFFFF),
                ),
              ),
              style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all<Color>(
                  color000000,
                ),
                shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                )),
                padding: MaterialStateProperty.all(
                    EdgeInsets.symmetric(vertical: 16.0, horizontal: 20.0)),
              ),
            ),
          ),
        ],
      );

  _removeFocus() {
    FocusScope.of(context).unfocus();
  }

  _onContinueTapped() async {
    String carNumber = _controller.text;
    String? result = Validator.validateVehicleNumber(carNumber,
        customErrorMessage: vehicle_error_constants.invalid.car);
    if (result != null) {
      setState(() {
        _carNumberError = result.replaceAll('car', 'vehicle');
      });
    } else {
      _removeFocus();
      setState(() {
        _carNumberError = null;
      });

      await _createAssetBloc!.hasRateLimitExceeded(carNumber);

      AnalyticsTrackerManager.instance.sendEvent(
          event: TapConstants.ADD_ASSET_VEHICLE_NUMBER_ENTERED,
          properties: {
            "registration_number": carNumber,
          });

      await R2D2Events.instance.trackAssetManagementButtonTappedEvents(
          'add_asset_vehicle_number_entered', 'create_asset',
          regNumber: carNumber);

      AnalyticsTrackerManager.instance.sendEvent(
          event: TapConstants.TAP_BTN_CONTINUE_ADD_ASSET,
          properties: {
            "registration_number": carNumber,
          });

      await R2D2Events.instance.trackAssetManagementButtonTappedEvents(
          'tap_btn_continue_add_asset', 'create_asset',
          regNumber: carNumber);
    }
  }

  onRetry() async {
    _createAssetBloc?.resetToInitState();
    AnalyticsTrackerManager.instance.sendEvent(
        event: TapConstants.TAP_BTN_TRY_AGAIN_TO_FETCH_RECORDS,
        properties: {
          "registration_number": _controller.text,
        });
    await R2D2Events.instance.trackAssetManagementButtonTappedEvents(
        'tap_btn_try_again_to_fetch_records', 'create_asset',
        regNumber: _controller.text);
  }

  bool get isScrollControllerLinkedToView =>
      _scrollController.positions.isNotEmpty;
}
